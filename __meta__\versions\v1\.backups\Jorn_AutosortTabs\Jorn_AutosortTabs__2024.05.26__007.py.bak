import sublime
import sublime_plugin

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    def __init__(self):
        self.last_activated_view = None
        self.previous_actions = {}
        self.view_positions = {}

    def on_activated_async(self, view):
        self.last_activated_view = view
        view_id = view.id()
        if view_id not in self.previous_actions:
            self.previous_actions[view_id] = []
        if view_id not in self.view_positions:
            window = sublime.active_window()
            group, index = window.get_view_index(view)
            self.view_positions[view_id] = (group, index)
            view.settings().set('original_content', view.substr(sublime.Region(0, view.size())))  # Store initial content

    def on_modified_async(self, view):
        settings = sublime.load_settings('Jorn_AutosortTabs.sublime-settings')
        trigger_condition = settings.get("autosort_trigger_condition", "keypress_after_switch")

        view_id = view.id()
        previous_action = self.previous_actions.get(view_id, [])[-1] if self.previous_actions.get(view_id) else None
        current_content = view.substr(sublime.Region(0, view.size()))

        should_trigger = False

        if (trigger_condition == "keypress_after_switch" and view == self.last_activated_view
            and not view.is_scratch() and current_content != view.settings().get('original_content')):
            should_trigger = True
        elif trigger_condition == "save_file" and view.is_dirty() and previous_action == "save_file":
            should_trigger = True
        elif trigger_condition == "close_file" and previous_action == "close_file":
            should_trigger = True

        if should_trigger:
            self.move_active_to_top()
            self.previous_actions[view_id].append("autosort")
            view.settings().set('original_content', current_content)  # Update original content
        else:
            if previous_action == "autosort":
                self.revert_view_position(view)  # Revert if conditions not met

    def move_active_to_top(self):
        window = sublime.active_window()
        view = window.active_view()
        view_id = view.id()
        group, index = window.get_view_index(view)

        if index > 0:
            print(f"Moving view {view_id} to top")  # Logging
            self.view_positions[view_id] = (group, index)
            window.set_view_index(view, group, 0)
            self.previous_actions[view_id].append("autosort")

    def revert_view_position(self, view):
        view_id = view.id()
        if view_id in self.view_positions:
            window = sublime.active_window()
            group, index = self.view_positions[view_id]
            print(f"Reverting view {view_id} to position ({group}, {index})")  # Logging
            window.set_view_index(view, group, index)
