import sublime
import sublime_plugin
import os
import time
from collections import defaultdict, deque

PLUGIN_NAME = "Jorn_AutosortTabs"
MAX_REORDERS_PER_SECOND = 3


class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    """
    Main plugin that:
      - Tracks pinned tabs (pinned_tabs).
      - Places pinned tabs at the front (in left-to-right order).
      - Optionally autosorts unpinned tabs behind pinned on activation if user settings say so.
      - Prevents infinite loops via:
        1) Recursion guard (_is_reordering),
        2) Arrangement check (skip reorder if already correct),
        3) Frequency-based limit (MAX_REORDERS_PER_SECOND).
    """

    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self.pinned_tabs = defaultdict(lambda: defaultdict(set))
        self._is_reordering = False
        self._last_arrangements = defaultdict(lambda: defaultdict(tuple))
        self._reorder_timestamps = deque()
        Jorn_AutosortTabsCommand._instance = self
        self._debug_print(f"{PLUGIN_NAME} initialized")

    @classmethod
    def instance(cls):
        """
        Used by pin/unpin commands to reference this plugin instance directly.
        """
        return cls._instance

    def _debug_print(self, message):
        """
        Print debug message only if debug mode is enabled in settings.
        """
        if self.settings.get("enable_debug_prints", False):
            print(f"[DEBUG] {message}")

    # -------------------------------------------------------------------------
    # Sublime Event Hooks
    # -------------------------------------------------------------------------
    def on_activated_async(self, view):
        """
        If pinned, reorder the group to ensure pinned remain at the front.
        If unpinned and meets "autosort" conditions, move it behind pinned tabs.
        """

        if not view or not view.window():
            return
        if self._is_reordering:
            return

        window = view.window()
        group, view_index = window.get_view_index(view)

        self._debug_print(f"on_activated_async: view_id={view.id()}, group={group}, index={view_index}")

        if self._is_pinned(view):
            self._debug_print("View is pinned, reordering group")
            self._reorder_group(window, group)
        else:
            if not self._should_process(view):
                self._debug_print("Skipping unpinned view (no autosort)")
                return

            pinned_count = len(self.pinned_tabs[window.id()][group])
            if view_index > pinned_count:
                if not self._check_reorder_frequency():
                    return
                self._is_reordering = True
                try:
                    window.set_view_index(view, group, pinned_count)
                finally:
                    self._is_reordering = False

        # Update visual indicators for all tabs in this group
        self._update_tab_decorations(window, group)

    def on_load_async(self, view):
        """
        Update decorations when a file is loaded.
        """
        if not view or not view.window():
            return
        window = view.window()
        group, _ = window.get_view_index(view)
        self._update_tab_decorations(window, group)

    def on_new_async(self, view):
        """
        Update decorations when a new view is created.
        """
        if not view or not view.window():
            return
        window = view.window()
        group, _ = window.get_view_index(view)
        self._update_tab_decorations(window, group)

    def on_close(self, view):
        """
        Remove closed views from pinned data sets.
        """
        view_id = view.id() if view else None

        if view and view.window():
            window = view.window()
            group, _ = window.get_view_index(view)
            # Store for later decoration update
            update_window = window
            update_group = group

        # Clean up color scheme storage
        if hasattr(self, '_original_color_schemes') and view_id in self._original_color_schemes:
            del self._original_color_schemes[view_id]

        for w_id, group_map in list(self.pinned_tabs.items()):
            for g_id, pinned_set in list(group_map.items()):
                pinned_set.discard(view_id)

        # Update visual indicators for the affected group
        if 'update_window' in locals() and 'update_group' in locals():
            self._update_tab_decorations(update_window, update_group)

    def on_post_window_command(self, window, command_name, args):
        """
        After possible tab-drag commands, sync pinned data and reorder to keep pinned in front.
        """
        if command_name in ("drag_select", "drag_drop", "move_tab"):
            self._sync_pinned_data(window)
            self._reorder_all_groups_in_window(window)

            # Update decorations after reordering
            for group in range(window.num_groups()):
                self._update_tab_decorations(window, group)

    # -------------------------------------------------------------------------
    # Public Pin/Unpin
    # -------------------------------------------------------------------------
    def pin_tab(self, view):
        """
        Pin a tab, then reorder group so pinned appear at the front.
        """
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        view_id = view.id()
        self._debug_print(f"Pinning tab: view_id={view_id}, group={group}, file={view.file_name() or 'untitled'}")
        self.pinned_tabs[window.id()][group].add(view_id)
        self._reorder_group(window, group)
        self._update_tab_decorations(window, group)

    def unpin_tab(self, view):
        """
        Unpin a tab, reorder group to keep pinned at front.
        """
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        view_id = view.id()
        self._debug_print(f"Unpinning tab: view_id={view_id}, group={group}, file={view.file_name() or 'untitled'}")
        self.pinned_tabs[window.id()][group].discard(view_id)
        self._reorder_group(window, group)
        self._update_tab_decorations(window, group)

    # -------------------------------------------------------------------------
    # Internal Helpers
    # -------------------------------------------------------------------------
    def _update_tab_decorations(self, window, group):
        """
        Add visual indicators to pinned tabs using both gutter icons and theme-based tab styling.
        """
        self._debug_print(f"Updating tab decorations for window={window.id()}, group={group}")

        if not self.settings.get("show_pin_indicators", True):
            self._debug_print("Pin indicators disabled, removing all")
            # If indicators disabled, remove all
            views_in_group = window.views_in_group(group)
            for view in views_in_group:
                view.erase_regions("jorn_pinned_tab_indicator")
                view.settings().erase("jorn_autosort_is_pinned")
            return

        views_in_group = window.views_in_group(group)
        pinned_ids = self.pinned_tabs[window.id()][group]
        self._debug_print(f"Found {len(pinned_ids)} pinned tabs in this group: {pinned_ids}")

        indicator_style = self.settings.get("pin_indicator_style", "icon")
        self._debug_print(f"Current indicator style: {indicator_style}")

        # First, store original color schemes if not already stored
        if not hasattr(self, '_original_color_schemes'):
            self._original_color_schemes = {}
            self._debug_print("Initialized _original_color_schemes")

        for view in views_in_group:
            view_id = view.id()
            view_name = view.file_name() or "untitled"

            if view_id in pinned_ids:
                self._debug_print(f"Decorating pinned tab: view_id={view_id}, file={view_name}")

                # 1. Add icon in gutter if style includes "icon"
                if indicator_style in ["icon", "both", "theme"]:
                    view.add_regions(
                        "jorn_pinned_tab_indicator",
                        [sublime.Region(0, 0)],
                        "region.orangish",
                        "bookmark",
                        sublime.DRAW_NO_FILL | sublime.DRAW_NO_OUTLINE
                    )
                    self._debug_print(f"Added bookmark icon to {view_id}")

                # 2. Apply theme settings for pinned tabs
                if indicator_style in ["theme", "both"]:
                    # Make sure we set this as a view-specific setting
                    view.settings().set("jorn_autosort_is_pinned", True)
                    self._debug_print(f"Set jorn_autosort_is_pinned=True for theme styling on {view_id}")
                else:
                    view.settings().erase("jorn_autosort_is_pinned")

                # 3. Also apply color scheme if style is "both" or "color"
                if indicator_style in ["color", "both"] and self.settings.get("use_color_scheme", False):
                    self._debug_print(f"Attempting to apply color scheme to {view_id}")
                    # Save original color scheme if not already saved
                    if view_id not in self._original_color_schemes:
                        original_scheme = view.settings().get('color_scheme', None)
                        self._original_color_schemes[view_id] = original_scheme
                        self._debug_print(f"Saved original color scheme: {original_scheme}")

                    # Apply pinned tab color scheme
                    color_scheme_path = "Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme"
                    self._debug_print(f"Setting color_scheme to {color_scheme_path}")
                    view.settings().set("color_scheme", color_scheme_path)

                    # Verify it was set
                    current_scheme = view.settings().get('color_scheme', None)
                    self._debug_print(f"After setting, color_scheme is now: {current_scheme}")

                elif view_id in self._original_color_schemes and self.settings.get("use_color_scheme", False):
                    # Restore color scheme if not using color style anymore
                    original_scheme = self._original_color_schemes[view_id]
                    self._debug_print(f"Restoring original color scheme: {original_scheme}")
                    if original_scheme:
                        view.settings().set("color_scheme", original_scheme)
                    else:
                        view.settings().erase("color_scheme")
                    del self._original_color_schemes[view_id]
            else:
                self._debug_print(f"Removing decorations from unpinned tab: view_id={view_id}, file={view_name}")
                # Remove all indicators for unpinned tabs

                # 1. Remove icon indicator
                view.erase_regions("jorn_pinned_tab_indicator")

                # 2. Remove theme setting
                view.settings().erase("jorn_autosort_is_pinned")

                # 3. Restore original color scheme if we were using it
                if view_id in self._original_color_schemes and self.settings.get("use_color_scheme", False):
                    original_scheme = self._original_color_schemes[view_id]
                    self._debug_print(f"Restoring original color scheme for unpinned tab: {original_scheme}")
                    if original_scheme:
                        view.settings().set("color_scheme", original_scheme)
                    else:
                        view.settings().erase("color_scheme")
                    del self._original_color_schemes[view_id]

    def _should_process(self, view):
        """
        Return True if the plugin is enabled and the view meets autosort conditions.
        """
        if not self.settings.get("autosort_on_tab_activated", False):
            return False
        return self._meets_sorting_conditions(view)

    def _meets_sorting_conditions(self, view):
        """
        Check if the tab is considered 'deleted', 'modified', 'saved', or 'unsaved'
        according to user settings.
        """
        file_name = view.file_name()
        if not file_name:
            return self.settings.get("autosort_unsaved_tabs", False)
        if not os.path.exists(file_name):
            return self.settings.get("autosort_deleted_tabs", False)
        if view.is_dirty():
            return self.settings.get("autosort_modified_tabs", False)
        return self.settings.get("autosort_saved_tabs", False)

    def _is_pinned(self, view):
        window = view.window()
        if not window:
            return False
        group, _ = window.get_view_index(view)
        is_pinned = view.id() in self.pinned_tabs[window.id()][group]
        self._debug_print(f"is_pinned check: view_id={view.id()}, result={is_pinned}")
        return is_pinned

    def _compute_arrangement_tuple(self, window, group, desired_views):
        """
        Represent pinned/unpinned arrangement as a tuple, e.g.: ('P:101', 'P:105', 'U:103')
        """
        pinned_ids = self.pinned_tabs[window.id()][group]
        result = []
        for v in desired_views:
            if v.id() in pinned_ids:
                result.append(f"P:{v.id()}")
            else:
                result.append(f"U:{v.id()}")
        return tuple(result)

    def _reorder_group(self, window, group):
        """
        Rebuild pinned-first arrangement in a single pass.
        Then skip if arrangement is already correct (arrangement check).
        Then check rate limit, then do the reorder if needed.
        """

        if not window or group < 0:
            return

        if self._is_reordering:
            return

        views_in_group = window.views_in_group(group)
        pinned_ids = self.pinned_tabs[window.id()][group]

        pinned_views = []
        unpinned_views = []
        for v in views_in_group:
            if v.id() in pinned_ids:
                pinned_views.append(v)
            else:
                unpinned_views.append(v)

        desired_order = pinned_views + unpinned_views

        # 1) Arrangement check: skip if already correct
        arrangement_tuple = self._compute_arrangement_tuple(window, group, desired_order)
        prev_tuple = self._last_arrangements[window.id()][group]
        if arrangement_tuple == prev_tuple:
            return

        # 2) Check reorder frequency
        if not self._check_reorder_frequency():
            return  # too many reorders happening => skip

        # 3) Perform reorder
        self._is_reordering = True
        try:
            for idx, v in enumerate(desired_order):
                _, view_index = window.get_view_index(v)
                if view_index != idx:
                    window.set_view_index(v, group, idx)
        finally:
            self._is_reordering = False

        # 4) Update last arrangement
        self._last_arrangements[window.id()][group] = arrangement_tuple

    def _reorder_all_groups_in_window(self, window):
        """
        Reorder pinned vs unpinned in all groups for the given window.
        """
        if not window:
            return
        for g_id in range(window.num_groups()):
            self._reorder_group(window, g_id)

    def _sync_pinned_data(self, window):
        """
        If pinned tabs have been moved between groups, update pinned_tabs accordingly.
        """
        if not window:
            return
        w_id = window.id()

        group_views = {}
        for group_index in range(window.num_groups()):
            group_views[group_index] = {v.id() for v in window.views_in_group(group_index)}

        new_pinned = defaultdict(set)
        for old_group, pinned_set in self.pinned_tabs[w_id].items():
            for vid in pinned_set:
                found = False
                for new_g, views_in_g in group_views.items():
                    if vid in views_in_g:
                        new_pinned[new_g].add(vid)
                        found = True
                        break
                # if not found, it's presumably closed => skip

        self.pinned_tabs[w_id] = new_pinned

    # -------------------------------------------------------------------------
    # Frequency-based Loop Prevention
    # -------------------------------------------------------------------------
    def _check_reorder_frequency(self):
        """
        Rate-limit reorder calls to avoid infinite loops triggered externally.
        """
        now = time.time()
        cutoff = now - 1.0
        while self._reorder_timestamps and self._reorder_timestamps[0] < cutoff:
            self._reorder_timestamps.popleft()

        if len(self._reorder_timestamps) >= MAX_REORDERS_PER_SECOND:
            return False

        self._reorder_timestamps.append(now)
        return True


# -------------------------------------------------------------------------
# Single Toggle Command for Pin/Unpin
# -------------------------------------------------------------------------
class JornAutosortTogglePinTabCommand(sublime_plugin.TextCommand):
    """
    Toggle/Pin/Unpin the right-clicked tab.
    """

    def run(self, edit, action="toggle", group=-1, index=-1, **kwargs):
        window = self.view.window()
        if not window:
            return

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return

        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return

        pinned = plugin._is_pinned(target_view)

        if action == "pin":
            if pinned:
                sublime.status_message("[Jorn Autosort] Tab is already pinned.")
            else:
                plugin.pin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab pinned.")
        elif action == "unpin":
            if pinned:
                plugin.unpin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab unpinned.")
            else:
                sublime.status_message("[Jorn Autosort] Tab is not pinned.")
        elif action == "toggle":
            if pinned:
                plugin.unpin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab unpinned.")
            else:
                plugin.pin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab pinned.")
        else:
            sublime.status_message(f"[Jorn Autosort] Unknown action: {action}")

    def is_visible(self, action="toggle", group=-1, index=-1, **kwargs):
        """
        Dynamically updated context menu
        - Hides the pin/unpin group depending on the current group lock state.
        """
        if action not in ("pin", "unpin", "toggle"):
            return False

        window = self.view.window()
        if not window:
            return False

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return False

        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return False

        pinned = plugin._is_pinned(target_view)
        if action == "pin":
            return not pinned
        elif action == "unpin":
            return pinned
        return True  # 'toggle' is always visible

# -------------------------------------------------------------------------
# Toggle Autosort on Tab Activated Command
# -------------------------------------------------------------------------
class JornToggleAutosortOnTabActivatedCommand(sublime_plugin.ApplicationCommand):
    """
    Toggle the "autosort_on_tab_activated" setting.
    """
    def run(self):
        settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        current_value = settings.get("autosort_on_tab_activated", False)
        settings.set("autosort_on_tab_activated", not current_value)
        sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")

        new_value = not current_value
        status = "enabled" if new_value else "disabled"
        sublime.status_message(f"[Jorn Autosort] Autosort on tab activation {status}")


class JornCyclePinIndicatorStyleCommand(sublime_plugin.ApplicationCommand):
    """
    Cycle through available pin indicator styles.
    """
    def run(self):
        settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        styles = ["icon", "color", "both", "theme", "none"]
        current_style = settings.get("pin_indicator_style", "icon")

        if not settings.get("show_pin_indicators", True):
            # If indicators are disabled, enable them with default style
            settings.set("show_pin_indicators", True)
            settings.set("pin_indicator_style", "icon")
            style_msg = "icon"
        else:
            # If indicators are enabled, cycle through styles
            try:
                current_index = styles.index(current_style)
                next_index = (current_index + 1) % len(styles)
                next_style = styles[next_index]
            except ValueError:
                next_style = "icon"  # Default if current style is invalid

            if next_style == "none":
                settings.set("show_pin_indicators", False)
                style_msg = "disabled"
            else:
                settings.set("pin_indicator_style", next_style)
                style_msg = next_style

        sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")

        # Update all tabs with new style
        instance = Jorn_AutosortTabsCommand.instance()
        if instance:
            for window in sublime.windows():
                for group in range(window.num_groups()):
                    instance._update_tab_decorations(window, group)

        sublime.status_message(f"[Jorn Autosort] Pin indicators: {style_msg}")


class JornRestoreOriginalThemeCommand(sublime_plugin.ApplicationCommand):
    """
    Restore the original theme that was active before any theme changes.
    """
    def run(self):
        plugin_settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        original_theme = plugin_settings.get("original_theme", None)

        if original_theme:
            prefs = sublime.load_settings("Preferences.sublime-settings")
            current = prefs.get("theme", "")

            # Only restore if current theme appears to be one of ours
            if "JornPinnedTab" in current or "PinnedTab" in current:
                prefs.set("theme", original_theme)
                sublime.save_settings("Preferences.sublime-settings")
                sublime.status_message(f"[Jorn Autosort] Restored original theme: {original_theme}")
            else:
                sublime.status_message("[Jorn Autosort] Theme wasn't changed, no need to restore")
        else:
            sublime.status_message("[Jorn Autosort] No original theme saved to restore")


def plugin_loaded():
    """
    Initialize tab decorations and apply theming when plugin is loaded.
    """
    # Load settings to check if debug is enabled
    settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
    debug_enabled = settings.get("enable_debug_prints", False)

    if debug_enabled:
        print(f"[DEBUG] {PLUGIN_NAME} plugin_loaded called")

    # Approach 1: Add our setting to theme_settings for better compatibility
    try:
        # Add the 'jorn_autosort_is_pinned' key to the list of settings that get
        # checked by the theme system (otherwise view-specific settings don't affect themes)
        prefs = sublime.load_settings("Preferences.sublime-settings")
        settings_list = prefs.get("theme_settings", [])
        if "jorn_autosort_is_pinned" not in settings_list:
            settings_list.append("jorn_autosort_is_pinned")
            prefs.set("theme_settings", settings_list)
            sublime.save_settings("Preferences.sublime-settings")
            if debug_enabled:
                print("[DEBUG] Added jorn_autosort_is_pinned to theme_settings")
        else:
            if debug_enabled:
                print("[DEBUG] jorn_autosort_is_pinned already in theme_settings")
    except Exception as e:
        if debug_enabled:
            print(f"[DEBUG] Error setting up theme_settings: {str(e)}")

    # Approach 2: Try to directly change the theme
    try:
        # Get current theme
        prefs = sublime.load_settings("Preferences.sublime-settings")
        current_theme = prefs.get("theme", "")
        if debug_enabled:
            print(f"[DEBUG] Current theme: {current_theme}")

        # Save original theme if not already saved
        plugin_settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        if not plugin_settings.has("original_theme"):
            plugin_settings.set("original_theme", current_theme)
            sublime.save_settings(f"{PLUGIN_NAME}.sublime-settings")
            if debug_enabled:
                print(f"[DEBUG] Saved original theme: {current_theme}")

        # Check plugin settings
        if debug_enabled:
            print(f"[DEBUG] show_pin_indicators: {plugin_settings.get('show_pin_indicators', True)}")
            print(f"[DEBUG] pin_indicator_style: {plugin_settings.get('pin_indicator_style', 'icon')}")
            print(f"[DEBUG] use_color_scheme: {plugin_settings.get('use_color_scheme', False)}")

        # If pinned tab indicators are enabled and style is "theme",
        # set our theme directly
        if (plugin_settings.get("show_pin_indicators", True) and
                plugin_settings.get("pin_indicator_style", "icon") == "theme"):
            # prefs.set("theme", "JornPinnedTab.sublime-theme")
            # sublime.save_settings("Preferences.sublime-settings")
            if debug_enabled:
                print("[DEBUG] Would set theme to JornPinnedTab.sublime-theme, but disabled for now")
            pass  # Disabled direct theme switching for now - use the more compatible first approach
    except Exception as e:
        if debug_enabled:
            print(f"[DEBUG] Error setting theme: {str(e)}")

    # Check if color scheme files exist
    color_scheme_path = os.path.join(sublime.packages_path(), "Jorn_AutosortTabs", "JornPinnedTab.sublime-color-scheme")
    if debug_enabled:
        print(f"[DEBUG] Checking if color scheme exists at: {color_scheme_path}")
        print(f"[DEBUG] File exists: {os.path.isfile(color_scheme_path)}")

    # Update decorations for all tabs
    instance = Jorn_AutosortTabsCommand.instance()
    if instance:
        if debug_enabled:
            print("[DEBUG] Updating decorations for all tabs")
        for window in sublime.windows():
            for group in range(window.num_groups()):
                instance._update_tab_decorations(window, group)
    else:
        if debug_enabled:
            print("[DEBUG] No plugin instance available yet")
