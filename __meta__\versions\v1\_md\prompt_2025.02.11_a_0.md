System Prompt:
- You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.

Context:
- You're working on the plugin for Sublime Text 4 (build 4089) called `Jorn_AutosortTabs`.

Goal:
- Prepare code for the capability sort/autosort based on conditions (for applying conditional actions in a sequential order).

Code:


    #### `Jorn_AutosortTabs.py`

    ```python
    import sublime
    import sublime_plugin
    import os

    # Constants
    # =======================================================
    _SETTINGS_FILE = "Jorn_AutosortTabs.sublime-settings"


    # ...
    # =======================================================
    class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):

        def __init__(self):

            # Load settings
            self.settings = sublime.load_settings(_SETTINGS_FILE)

            # Instance variables
            self.tab_file_path = ""
            self.tab_file_name = ""

        # On tab activated
        def on_activated(self, view):

            # move to top (defaults to False)
            if self.settings.get("autosort_on_tab_activated", False):
                self.move_active_to_top()

            # print info: (defaults to False)
            if self.settings.get("print_on_tab_activation", False):
                if view and view.window() and view.file_name():
                    self.tab_file_path = view.file_name()
                    self.tab_file_name = os.path.basename(self.tab_file_path)
                    print(f'Jorn_AutosortTabs: Activated [{self.tab_file_name}]')
                else:
                    print('Jorn_AutosortTabs: Activated [Non-file view]')


        def on_selection_modified(self, view):
            pass # Not sure if i will use this later.

        def is_valid_file_view(self, view):
            # Ensure the view is a text buffer and has a file associated with it
            return view.file_name() is not None and not view.is_scratch()

        def move_active_to_top(self):
            window, group, index, view = self.get_active_view_index()
            if group == -1 or index == -1:
                return
            if index != 0:
                window.set_view_index(view, group, 0)

        def get_active_view_index(self):
            window = sublime.active_window()
            group = window.active_group()
            view = window.active_view_in_group(group)
            if view:
                _, index = window.get_view_index(view)
                return window, group, index, view
            return window, group, -1, None
    ```


    #### `Jorn_AutosortTabs.sublime-commands`

    ```sublime-commands
    [
        {
            "caption": "Jorn - Toggle Sticky Tab",
            "command": "jorn_toggle_sticky_tab"
        }
    ]
    ```


    #### `Jorn_AutosortTabs.sublime-keymap`

    ```sublime-keymap
    [
    ]
    ```


    #### `Jorn_AutosortTabs.sublime-settings`

    ```sublime-settings
    {
        "autosort_on_tab_activated": true,
        "print_on_tab_activation": false,

        "enable_sticky_tabs": true,
        "sticky_tabs_list": [],  // or ["C:/myProject/main.py"]
        "sticky_tabs_patterns": [], // or ["*.log", "*_config.*"]

        // If true, pinned tab you just activated or pinned is moved
        // to the front among pinned tabs
        "reorder_pinned_on_activation": false
    }
    ```


    #### `Tab Context.sublime-menu`

    ```sublime-menu
    // File: Tab Context.sublime-menu
    [
        { "caption": "-" },
        {
            "caption": "Toggle Sticky Tab",
            "command": "jorn_toggle_sticky_tab"
        }
    ]
    ```
