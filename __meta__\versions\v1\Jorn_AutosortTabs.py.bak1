import sublime
import sublime_plugin
import os
import fnmatch
import logging

PLUGIN_NAME = "JornAutosortTabs"

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(PLUGIN_NAME)

class JornAutosortTabs:

    excluded_from_autosort = {}
    settings = None

    @classmethod
    def load_settings(cls):
        try:
            cls.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        except Exception as e:
            logger.error(f"Failed to load settings: {e}")

    @classmethod
    def toggle_tab_autosort(cls, file_path):
        groups = cls.excluded_from_autosort.setdefault(window_id, set())
        if group_index in groups:
            groups.remove(group_index)
            state = "UNLOCKED"
        else:
            groups.add(group_index)
            state = "LOCKED"
        return state

    @classmethod
    def is_group_locked(cls, window_id, group_index):
        return group_index in cls.excluded_from_autosort.get(window_id, set())

    @classmethod
    def get_extension_lock_info(cls, window_id, group_index):
        grp_data = cls.extension_lock_data.setdefault(window_id, {})
        info = grp_data.setdefault(group_index, {"enabled": False, "extensions": set()})
        return info

    @classmethod
    def set_extension_lock_enabled(cls, window_id, group_index, enabled):
        info = cls.get_extension_lock_info(window_id, group_index)
        info["enabled"] = enabled

    @classmethod
    def update_group_extensions(cls, window, group_idx):
        info = cls.get_extension_lock_info(window.id(), group_idx)
        exts = set()
        for v in window.views_in_group(group_idx):
            if v.file_name():
                exts.add(os.path.splitext(v.file_name())[1].lower())
        info["extensions"] = exts

    @classmethod
    def add_extension_to_group(cls, window_id, group_idx, file_ext):
        info = cls.get_extension_lock_info(window_id, group_idx)
        if info["enabled"]:
            info["extensions"].add(file_ext.lower())


# Constants
# =======================================================
_SETTINGS_FILE = "JornAutosortTabs.sublime-settings"


# ...
# =======================================================
class JornAutosortTabsCommand(sublime_plugin.EventListener):

    def __init__(self):

        # Load settings
        self.settings = sublime.load_settings(_SETTINGS_FILE)

        # Instance variables
        self.tab_file_path = ""
        self.tab_file_name = ""
        self.tabs_autosort_disabled = []

        # Sort conditions
        # self.sort_conditions = {
        #     "1": ".md"
        # }

        # # Sort order rules
        # self.sort_conditions = {
        #         }

    # On tab activated
    def on_activated(self, view):

        # if self.settings.get("autosort_on_tab_activated", False):
        if self.settings.get("print_on_tab_activation", False):
            if not self.is_valid_file_view(view):
                return
            print(f'self.is_autosort_enabled(view): {self.is_autosort_enabled(view)}')
            if self.is_autosort_enabled(view):
                return

        # # print info: (defaults to False)
        # if self.settings.get("print_on_tab_activation", False):
        #     # file tab
        #     if view and view.window() and view.file_name():
        #         self.tab_file_path = view.file_name()
        #         self.tab_file_name = os.path.basename(self.tab_file_path)
        #         print(f'JornAutosortTabs: Activated [{self.tab_file_path}]')
        #         # print(f'JornAutosortTabs: Activated [{self.tab_file_name}]')
        #     # unsaved tab
        #     else:
        #         print('JornAutosortTabs: Activated [Non-file view]')

        # # move to top (defaults to False)
        # if self.settings.get("autosort_on_tab_activated", False):
        #     self.move_active_to_top()



    def on_selection_modified(self, view):
        pass # Not sure if i will use this later.

    def is_valid_file_view(self, view):
        return view.file_name() is not None and not view.is_scratch()

    def is_autosort_enabled(self, view):

        # if not self.settings.get("enable_sticky_tabs", True):
        #     return False

        file_path = view.file_name()
        if not file_path:
            return False

        if file_path in self.tabs_autosort_disabled:
            print(f'file_path: {file_path}')
            return True


    def move_active_to_top(self):
        window, group, index, view = self.get_active_view_index()
        if group == -1 or index == -1:
            return
        if index != 0:
            window.set_view_index(view, group, 0)

    def get_active_view_index(self):
        window = sublime.active_window()
        group = window.active_group()
        view = window.active_view_in_group(group)
        if view:
            _, index = window.get_view_index(view)
            return window, group, index, view
        return window, group, -1, None

# class JornAutosortToggleCommand(sublime_plugin.TextCommand):
class JornAutosortToggleCommand(sublime_plugin.WindowCommand):

    def run(self, action):
        view = self.window.active_view()
        if not view:
            return

        file_path = view.file_name()
        if not file_path:
            sublime.status_message("JornAutosortTabs: Non-file view, cannot toggle exclude from autosort.")
            return

    #     window_id = self.window.id()
    #     group_idx, _ = self.window.get_view_index(view)
    #     locked = JornAutosortTabs.is_group_locked(window_id, group_idx)

    #     if action == "lock" and not locked:
    #         state = JornAutosortTabs.toggle_group_lock(window_id, group_idx)
    #         msg = f"Group {group_idx} in Window {window_id} {state}."
    #     elif action == "unlock" and locked:
    #         state = JornAutosortTabs.toggle_group_lock(window_id, group_idx)
    #         msg = f"Group {group_idx} in Window {window_id} {state}."
    #     elif action == "toggle":
    #         state = JornAutosortTabs.toggle_group_lock(window_id, group_idx)
    #         msg = f"Group {group_idx} in Window {window_id} {state}."
    #     else:
    #         state = "LOCKED" if locked else "UNLOCKED"
    #         msg = f"Group {group_idx} in Window {window_id} already {state}."

    #     sublime.status_message(f"[{PLUGIN_NAME}] {msg}")
    #     logger.info(msg)

    # def run(self, edit):
    #     view = self.view
    #     file_path = view.file_name()

    #     if not file_path:
    #         sublime.status_message("JornAutosortTabs: Non-file view, cannot toggle sticky.")
    #         return

    #     settings = sublime.load_settings(_SETTINGS_FILE)
    #     sticky_list = settings.get("sticky_tabs_list", [])
    #     file_name = os.path.basename(file_path)

    #     if file_path in sticky_list:
    #         sticky_list.remove(file_path)
    #         msg = f"Removed {file_name} from sticky tabs"
    #     else:
    #         sticky_list.append(file_path)
    #         msg = f"Added {file_name} to sticky tabs"

    #     settings.set("sticky_tabs_list", sticky_list)
    #     sublime.save_settings(_SETTINGS_FILE)
    #     sublime.status_message(f"JornAutosortTabs: {msg}")

    def is_visible(self, action):
        """
        Dynamically updated context menu
        - Hides the lock/unlock group depending on the current group lock state.
        """
        view = self.window.active_view()
        if not view:
            return False

        window_id = self.window.id()
        group_idx, _ = self.window.get_view_index(view)
        enabled = JornAutosortTabs.is_autosort_enabled(window_id, group_idx)

        view = self.window.active_view()
        if not view:
            return

        file_path = view.file_name()
        if not file_path:
            sublime.status_message("JornAutosortTabs: Non-file view, cannot toggle exclude from autosort.")
            return


        if action == "enable":
            return not enabled
        if action == "disable":
            return enabled
        return True

def plugin_loaded():
    JornAutosortTabs.load_settings()
