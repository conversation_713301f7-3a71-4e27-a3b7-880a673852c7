when writing plugins for sublime text that maintains a global tracking for all open tabs e.g. to trigger actions when new files are opened (hypotethical scenario below):

- goal: ability to set and maintain that certain panes/groups are set custom state "locked"
- clarification: when a pane/grop is locked, any new tab t hat is opened automatically will·be moved to a diferent pane than the one that's locked
- value: whenever opening a new tab/file it won't steal the focus from tabs you're working on
- behaviour: you will still be able to manually drag tabs over to the locked layout(s), but whenever opening a new file it will automatically be moved to (either the currently active layout, or) the next layout if the active layout is locked.
