
The goal is propose an optimal way to implement the ability to have "sticky tabs".


Here's the current code for the Sublime Text plugin:

    # Project Files Documentation for `Jorn_AutosortTabs`

    ### File Structure

    ```
    ├── Jorn_AutosortTabs.py
    ├── Jorn_AutosortTabs.sublime-keymap
    ├── Jorn_AutosortTabs.sublime-project
    └── Jorn_AutosortTabs.sublime-settings
    ```


    #### `Jorn_AutosortTabs.py`

    ```python
    import sublime
    import sublime_plugin
    import os

    # Constants
    # =======================================================
    _SETTINGS_FILE = "Jorn_AutosortTabs.sublime-settings"


    # ...
    # =======================================================
    class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):

        def __init__(self):

            # Load settings
            self.settings = sublime.load_settings(_SETTINGS_FILE)

            # Instance variables
            self.tab_file_path = ""
            self.tab_file_name = ""

        # On tab activated
        def on_activated(self, view):

            # move to top
            if self.settings.get("autosort_on_tab_activated", False):
                self.move_active_to_top()

            # print info
            if self.settings.get("print_on_tab_activation", False):
                if view and view.window() and view.file_name():
                    self.tab_file_path = view.file_name()
                    self.tab_file_name = os.path.basename(self.tab_file_path)
                    print(f'Activated: [{self.tab_file_name}]')
                elif self.settings.get("print_on_tab_activation", False) and view and view.window():
                    print('Activated: [Non-file view]')


        def on_selection_modified(self, view):
            pass # Not sure if i will use this later.

        def is_valid_file_view(self, view):
            # Ensure the view is a text buffer and has a file associated with it
            return view.file_name() is not None and not view.is_scratch()

        def move_active_to_top(self):
            window, group, index, view = self.get_active_view_index()
            if group == -1 or index == -1:
                return
            if index != 0:
                window.set_view_index(view, group, 0)

        def get_active_view_index(self):
            window = sublime.active_window()
            group = window.active_group()
            view = window.active_view_in_group(group)
            if view:
                _, index = window.get_view_index(view)
                return window, group, index, view
            return window, group, -1, None
    ```


    #### `Jorn_AutosortTabs.sublime-keymap`

    ```sublime-keymap
    [
    ]
    ```


    #### `Jorn_AutosortTabs.sublime-project`

    ```sublime-project
    {
    	"folders":
    	[
    		{
    			"path": "."
    		}
    	]
    }
    ```


    #### `Jorn_AutosortTabs.sublime-settings`

    ```sublime-settings
    {
        // "autosort_trigger_condition": "keypress_after_switch"  // or "save_file" or "close_file"
        // "autosort_trigger_condition": "keypress"  // or "always"
        // "autosort_on_tab_activated": false,  // or "keypress"
        "autosort_on_tab_activated": true,
        "print_on_tab_activation": false,
    }
    ```

