System Prompt:
- You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.

Context:
- You're working on the plugin for Sublime Text 4 (build 4089).

Goal:
- Improve intuitive readability by renaming classes and methods.
- Improve inherent readability by structuriong into logically ordered sections.
- Improve general readability by selectively adding very short but high-value comments where relevant.

Code:

    ```python
    import os
    import time
    import traceback
    import sublime
    import sublime_plugin

    ALL_CATEGORIES = [
        "empty_and_deleted",
        "empty_and_unsaved",
        "deleted_not_empty",
        "empty_not_deleted",
        "unsaved_not_empty",
        "clean_and_external",
        "clean_and_project",
        "dirty_and_external",
        "dirty_and_project",
    ]


    def is_in_project(view):
        file_path = view.file_name()
        if not file_path:
            return False
        window = view.window()
        if not window:
            return False
        project_folders = window.folders()
        return any(
            os.path.commonpath([file_path, folder]) == folder for folder in project_folders
        )


    class PhasedTabCategorizer:
        def __init__(self, window):
            self.window = window

        def categorize_phase1(self):
            categories_dict = {category: [] for category in ALL_CATEGORIES}
            all_views = self.window.views()
            for view in all_views:
                category_name = self.determine_category(view)
                if category_name not in categories_dict:
                    category_name = "empty_not_deleted"
                categories_dict[category_name].append(view)
            return categories_dict

        def determine_category(self, view):
            content_stripped = view.substr(sublime.Region(0, view.size())).strip()
            content_length = len(content_stripped)
            if not view.file_name():
                if content_length <= 1:
                    return "empty_and_unsaved"
                return "unsaved_not_empty"
            else:
                if not os.path.exists(view.file_name()):
                    if content_length <= 1:
                        return "empty_and_deleted"
                    return "deleted_not_empty"
                else:
                    if is_in_project(view):
                        if view.is_dirty():
                            return "dirty_and_project"
                        return "clean_and_project"
                    else:
                        if view.is_dirty():
                            return "dirty_and_external"
                        return "clean_and_external"

        def categorize_phase2(self):
            categories_dict = {category: [] for category in ALL_CATEGORIES}
            all_views = self.window.views()
            for view in all_views:
                category_name = self.determine_category(view)
                if category_name not in categories_dict:
                    category_name = "empty_not_deleted"
                tab_info = self.get_tab_info(view)
                categories_dict[category_name].append((view, tab_info))
            return categories_dict

        def get_tab_info(self, view):
            tab_info = {}
            try:
                if view.file_name():
                    tab_info["basename"] = os.path.basename(view.file_name())
                else:
                    tab_info["basename"] = "Untitled"
                tab_info["size_chars"] = view.size()
            except Exception as e:
                print(f"[get_tab_info] Error: {e}")
                print(traceback.format_exc())
                tab_info["basename"] = "Unknown"
                tab_info["size_chars"] = -1
            return tab_info

        def categorize_phase3(self):
            categories_dict = {category: [] for category in ALL_CATEGORIES}
            all_views = self.window.views()
            for view in all_views:
                category_name = self.determine_category(view)
                if category_name not in categories_dict:
                    category_name = "empty_not_deleted"
                tab_details = self.get_tab_details(view)
                categories_dict[category_name].append((view, tab_details))
            return categories_dict

        def get_tab_details(self, view):
            details = {
                "basename": "Untitled",
                "group": 0,
                "index": 0,
                "syntax": "Plain text",
                "line_count": 0,
            }
            try:
                window_instance = view.window()
                if window_instance:
                    group_id, index_id = window_instance.get_view_index(view)
                    details["group"] = group_id
                    details["index"] = index_id
                if view.file_name():
                    details["basename"] = os.path.basename(view.file_name())
                else:
                    details["basename"] = f"Untitled ({view.size()} chars)"
                syntax_path = view.settings().get("syntax")
                if syntax_path:
                    syntax_file = os.path.basename(syntax_path)
                    syntax_name, _ = os.path.splitext(syntax_file)
                    details["syntax"] = syntax_name
                details["line_count"] = view.rowcol(view.size())[0] + 1
            except Exception as e:
                print(f"[get_tab_details] Error: {e}")
                print(traceback.format_exc())
            return details


    class JornCategorizeTabsPhase1Command(sublime_plugin.WindowCommand):
        def run(self, categorize_and_print=False):
            if categorize_and_print:
                try:
                    tab_categorizer = PhasedTabCategorizer(self.window)
                    categorized_views = tab_categorizer.categorize_phase1()
                    self.print_results_phase1(categorized_views)
                except Exception as e:
                    print(f"[Phase1] Error: {e}")
                    print(traceback.format_exc())
            else:
                print("[Phase1] Use 'categorize_and_print=True' to output results.")

        def print_results_phase1(self, categorized_views):
            output_view = self.window.create_output_panel("jorn_phase1")
            output_view.set_read_only(False)
            output_view.run_command("erase_view_contents")
            panel_content = ""
            for category_name in ALL_CATEGORIES:
                items = categorized_views[category_name]
                panel_content += f"## {category_name} ({len(items)})\n"
                if items:
                    for view in items:
                        file_name = view.file_name()
                        if file_name:
                            base_name = os.path.basename(file_name)
                        else:
                            base_name = f"Untitled ({view.size()} chars)"
                        panel_content += f" - {base_name}\n"
                else:
                    panel_content += " - None\n"
                panel_content += "\n"
            output_view.run_command("insert_content", {"content": panel_content})
            output_view.set_read_only(True)
            self.window.run_command("show_panel", {"panel": "output.jorn_phase1"})


    class JornCategorizeTabsPhase2Command(sublime_plugin.WindowCommand):
        def run(self, categorize_and_print=False):
            if categorize_and_print:
                try:
                    tab_categorizer = PhasedTabCategorizer(self.window)
                    categorized_views = tab_categorizer.categorize_phase2()
                    self.print_results_phase2(categorized_views)
                except Exception as e:
                    print(f"[Phase2] Error: {e}")
                    print(traceback.format_exc())
            else:
                print("[Phase2] Use 'categorize_and_print=True' to output results.")

        def print_results_phase2(self, categorized_views):
            output_view = self.window.create_output_panel("jorn_phase2")
            output_view.set_read_only(False)
            output_view.run_command("erase_view_contents")
            panel_content = ""
            for category_name in ALL_CATEGORIES:
                items = categorized_views[category_name]
                panel_content += f"## {category_name} ({len(items)})\n"
                if items:
                    for view, info in items:
                        panel_content += (
                            f" - {info['basename']} (Size: {info['size_chars']} chars)\n"
                        )
                else:
                    panel_content += " - None\n"
                panel_content += "\n"
            output_view.run_command("insert_content", {"content": panel_content})
            output_view.set_read_only(True)
            self.window.run_command("show_panel", {"panel": "output.jorn_phase2"})


    class JornCategorizeTabsPhase3Command(sublime_plugin.WindowCommand):
        def run(self, categorize_and_print=False):
            if categorize_and_print:
                try:
                    tab_categorizer = PhasedTabCategorizer(self.window)
                    categorized_views = tab_categorizer.categorize_phase3()
                    self.print_results_phase3(categorized_views)
                except Exception as e:
                    print(f"[Phase3] Error: {e}")
                    print(traceback.format_exc())
            else:
                print("[Phase3] Use 'categorize_and_print=True' to output results.")

        def print_results_phase3(self, categorized_views):
            output_view = self.window.create_output_panel("jorn_phase3")
            output_view.set_read_only(False)
            output_view.run_command("erase_view_contents")
            panel_content = ""
            for category_name in ALL_CATEGORIES:
                items = categorized_views[category_name]
                panel_content += f"## {category_name} ({len(items)})\n"
                if items:
                    for view, details in items:
                        panel_content += (
                            f" - {details['basename']} (Group: {details['group']}, "
                            f"Index: {details['index']}, Syntax: {details['syntax']}, "
                            f"Lines: {details['line_count']})\n"
                        )
                else:
                    panel_content += " - None\n"
                panel_content += "\n"
            output_view.run_command("insert_content", {"content": panel_content})
            output_view.set_read_only(True)
            self.window.run_command("show_panel", {"panel": "output.jorn_phase3"})


    class EraseViewContentsCommand(sublime_plugin.TextCommand):
        def run(self, edit):
            self.view.erase(edit, sublime.Region(0, self.view.size()))


    class InsertContentCommand(sublime_plugin.TextCommand):
        def run(self, edit, content):
            self.view.insert(edit, self.view.size(), content)


    class JornEventListener(sublime_plugin.EventListener):
        def on_activated(self, view):
            try:
                view.last_accessed = time.time()
            except Exception as e:
                print(f"[JornEventListener] Error updating last_accessed: {e}")
                print(traceback.format_exc())

        def on_modified_async(self, view):
            try:
                view.last_accessed = time.time()
            except Exception as e:
                print(f"[JornEventListener] Error updating last_accessed: {e}")
                print(traceback.format_exc())
    ```
