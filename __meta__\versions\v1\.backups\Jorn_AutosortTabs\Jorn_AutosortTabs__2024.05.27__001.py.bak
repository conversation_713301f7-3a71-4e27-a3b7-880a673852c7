import sublime
import sublime_plugin
from collections import OrderedDict
import time

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):

    def __init__(self):
        self.activation_order = OrderedDict()
        self.last_activated_time = 0
        self.time_threshold = 1.0  # time in seconds within which a keypress triggers autosort

    def on_activated(self, view):
        self.last_activated_time = time.time()

    def on_modified(self, view):
        settings = sublime.load_settings('Jorn_AutosortTabs.sublime-settings')
        if settings.get("autosort_on_tab_activated"):
            current_time = time.time()
            if (current_time - self.last_activated_time) <= self.time_threshold:
                if view and view.window():
                    file_name = view.file_name() or "Untitled"
                    print('Activated file: %s' % (file_name))
                    self.move_active_to_top()

    def on_text_command(self, view, command_name, args):
        settings = sublime.load_settings('Jorn_AutosortTabs.sublime-settings')
        if settings.get("autosort_on_keypress"):
            current_time = time.time()
            if (current_time - self.last_activated_time) <= self.time_threshold:
                if view and view.window():
                    file_name = view.file_name() or "Untitled"
                    print('Activated file: %s' % (file_name))
                    self.move_active_to_top()

    def move_active_to_top(self):
        window, group, index, view = self.get_active_view_index()
        if group == -1 or index == -1:
            return
        if index != 0:
            window.set_view_index(view, group, 0)

    def get_active_view_index(self):
        window = sublime.active_window()
        group = window.active_group()
        view = window.active_view_in_group(group)
        _, index = window.get_view_index(view)
        return window, group, index, view
