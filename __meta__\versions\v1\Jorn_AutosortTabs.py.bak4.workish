import sublime
import sublime_plugin
import os
import logging

PLUGIN_NAME = "Jorn_AutosortTabs"

# Optional logger setup
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    """
    EventListener that automatically sorts (reorders) the active tab
    based on user-defined settings:
      - pinned tabs are always at the top (in the order they already appear)
      - non-pinned tabs get moved to the top of the "non-pinned" section
        if they match the autosort criteria (deleted, modified, saved, unsaved).
    """

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        # The guard to prevent infinite recursion:
        self.is_reordering = False

    # -----------------------------------
    # MAIN EVENT: on_activated
    # -----------------------------------
    def on_activated(self, view):
        """
        Called when a view is activated (tab is clicked or switched to).
        Checks plugin settings to see if we should reorder the tab, and if so,
        places pinned tabs at the top and moves the newly activated tab
        to the top of the non-pinned section.
        """
        # If we're already in the middle of reordering, skip to avoid recursion
        if self.is_reordering:
            return

        if not view or not view.window():
            return

        # If autosort is disabled altogether, stop.
        if not self.settings.get("autosort_on_tab_activated", False):
            return

        file_path = view.file_name()
        pinned_list = self.settings.get("pinned_tabs_list", [])

        # If this file is pinned, do nothing (no reorder)
        if file_path and file_path in pinned_list:
            if self.settings.get("print_on_tab_activation", False):
                print(f"[{PLUGIN_NAME}] Pinned tab activated, no reordering: {file_path}")
            return

        # If the tab meets user-defined criteria (deleted, saved, etc.), reorder
        if self.should_autosort(view):
            window = view.window()
            group = window.active_group()

            # Set the guard to True so we skip further on_activated calls
            self.is_reordering = True
            try:
                self.reorder_tabs(window, group, view)
            finally:
                # Always reset the guard, even if an error occurs
                self.is_reordering = False

            if self.settings.get("print_on_tab_activation", False):
                print(f"[{PLUGIN_NAME}] Autosort triggered for: {file_path}")

        else:
            # For debugging or logging if you like
            if self.settings.get("print_on_tab_activation", False):
                print(f"[{PLUGIN_NAME}] Autosort not triggered for: {file_path}")

    # -----------------------------------
    # HELPER: should_autosort
    # -----------------------------------
    def should_autosort(self, view):
        """
        Returns True if the current view matches user settings for autosort.
        This includes checks like:
         - autosort_deleted_tabs (file exists on disk?),
         - autosort_modified_tabs (is_dirty?),
         - autosort_saved_tabs (exists and not dirty),
         - autosort_unsaved_tabs (no file_name).
        """
        if not view:
            return False

        file_name = view.file_name()

        # "deleted" if file_path exists but not on disk
        if file_name and not os.path.exists(file_name):
            return self.settings.get("autosort_deleted_tabs", False)

        # If the file_name exists on disk
        if file_name and os.path.exists(file_name):
            if view.is_dirty():
                return self.settings.get("autosort_modified_tabs", False)
            else:
                return self.settings.get("autosort_saved_tabs", False)

        # If there's no file_name, it's an unsaved or scratch buffer
        if not file_name:
            return self.settings.get("autosort_unsaved_tabs", False)

        return False

    # -----------------------------------
    # HELPER: reorder_tabs
    # -----------------------------------
    def reorder_tabs(self, window, group, active_view):
        """
        Reorders tabs in the given window/group so that:
          - pinned tabs remain on top (in the order they already appear),
          - the `active_view` is placed at the top of the non-pinned group,
          - any remaining non-pinned tabs follow in their original order.
        """
        if not window or group == -1:
            return


        pinned_list = self.settings.get("pinned_tabs_list", [])

        # Gather all views in the current group
        views_in_group = window.views_in_group(group)

        # Partition pinned vs. non-pinned
        pinned_views = []
        non_pinned_views = []
        for v in views_in_group:
            fp = v.file_name()
            if fp and fp in pinned_list:
                pinned_views.append(v)
            else:
                non_pinned_views.append(v)

        print(f'pinned_views: {pinned_views}')
        print(f'non_pinned_views: {non_pinned_views}')
        # Move the active_view to the front of non_pinned_views, if present
        if active_view in non_pinned_views:
            non_pinned_views.remove(active_view)
            non_pinned_views.insert(0, active_view)

        # Rebuild the tab order: pinned first, then non-pinned
        idx = 0
        for pv in pinned_views:
            window.set_view_index(pv, group, idx)
            idx += 1

        for nv in non_pinned_views:
            window.set_view_index(nv, group, idx)
            idx += 1

    # -----------------------------------
    # (OPTIONAL) UTILITY: get_active_view_index
    # -----------------------------------
    def get_active_view_index(self):
        """
        Returns (window, group, index, view) for the currently active view.
        If anything fails, returns (window, group, -1, None).
        """
        window = sublime.active_window()
        group = window.active_group()
        view = window.active_view_in_group(group)
        if view:
            _, index = window.get_view_index(view)
            return window, group, index, view
        return window, group, -1, None

    # -----------------------------------
    # (OPTIONAL) UTILITY: add_tab_to_queue
    # -----------------------------------
    def add_tab_to_queue(self):
        """
        Example placeholder function if you want to collect
        tabs to sort for batch processing or debugging. Currently
        just logs details about the active view.
        """
        window, group, index, view = self.get_active_view_index()
        print(f"[{PLUGIN_NAME}] add_tab_to_queue() called with:")
        print(f" - window: {window}")
        print(f" - group : {group}")
        print(f" - index : {index}")
        print(f" - view  : {view}")

    # -----------------------------------
    # (OPTIONAL) UTILITY: move_active_to_top
    # -----------------------------------
    def move_active_to_top(self):
        """
        Forces the currently active tab to the top of its group (index 0).
        Does NOT differentiate pinned vs. unpinned. Provided for reference
        or if you want a quick manual command.
        """
        window, group, index, view = self.get_active_view_index()
        if group == -1 or index == -1 or not view:
            return
        if index != 0:
            self.is_reordering = True
            try:
                window.set_view_index(view, group, 0)
            finally:
                self.is_reordering = False


class Jorn_PinCurrentTabCommand(sublime_plugin.WindowCommand):
    """
    Command to pin or unpin the currently active tab.
    Stores pinned tabs in 'pinned_tabs_list' setting within
    Jorn_AutosortTabs.sublime-settings.
    """

    def run(self):
        view = self.window.active_view()
        if not view or not view.file_name():
            sublime.status_message("No file to pin/unpin.")
            return

        file_path = view.file_name()
        settings = sublime.load_settings("Jorn_AutosortTabs.sublime-settings")
        pinned = settings.get("pinned_tabs_list", [])

        if file_path in pinned:
            pinned.remove(file_path)
            sublime.status_message("Unpinned current tab.")
        else:
            pinned.append(file_path)
            sublime.status_message("Pinned current tab.")

        settings.set("pinned_tabs_list", pinned)
        sublime.save_settings("Jorn_AutosortTabs.sublime-settings")
