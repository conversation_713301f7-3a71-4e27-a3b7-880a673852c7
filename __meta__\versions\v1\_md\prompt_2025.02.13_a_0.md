System Prompt:
- You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.

Context:
- You're working on the plugin for Sublime Text 4 (build 4189) called `Jorn_AutosortTabs`. This Sublime Text plugin is a work-in-progress that aims to dynamically handle automatic tab positions based on user-defined rules and tab states. Here's how it should work:
    - If a tab is added to "pinned_tabs_list" it should be added to a group for topmost tabs, these will not automatically be reordered within their "group" by the automatic tab sorting but will always be *before* any tab that is not in the "pinned_tabs_list".
    - If a tab is clicked that is not in the "pinned_tabs_list" (and that corresponds with the conditions for which to apply autosort, e.g. clicking a saved tab with `autosort_saved_tabs` set to True), it should be moved to the topmost *after* the pinned tabs.

Goal:
- You goal is to outline a step by step plan for how we can build this plugin based on incrementally transforming the existing code.

Code:

    ```python
    import sublime
    import sublime_plugin
    import os
    import fnmatch
    import logging

    # Constants
    # =======================================================
    PLUGIN_NAME = "Jorn_AutosortTabs"

    # ...
    # =======================================================
    class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):

        def __init__(self):
            self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
            self.pinned_tabs_list = []
            self.tabs_to_sort = []


        # On tab activated
        def on_activated(self, view):

            # Disabled
            autosort_enabled = self.settings.get("autosort_on_tab_activated", False)
            if not autosort_enabled or not (view and view.window()):
                return

            # Enabled
            else:
                # Is Pinned Tab
                print(f'self.pinned_tabs_list: {self.pinned_tabs_list}')
                if view.file_name() in self.pinned_tabs_list:
                    if self.settings.get("print_on_tab_activation", False):
                        print(f'pinned_tabs_list: {pinned_tabs_list}')
                    return

                # Is Deleted Tab
                if view.file_name() and not os.path.exists(view.file_name()):
                    autosort_deleted = self.settings.get("autosort_deleted_tabs", False)
                    if autosort_deleted:
                        self.add_tab_to_queue()
                    if self.settings.get("print_on_tab_activation", False):
                        tab_file_name = os.path.basename(view.file_name())
                        print(f'name: {tab_file_name} | autosort_deleted: {autosort_deleted}')

                # Is Modified Tab
                elif view.file_name() and os.path.exists(view.file_name()) and view.is_dirty():
                    autosort_modified = self.settings.get("autosort_modified_tabs", False)
                    if autosort_modified:
                        self.add_tab_to_queue()
                    if self.settings.get("print_on_tab_activation", False):
                        tab_file_name = os.path.basename(view.file_name())
                        print(f'name: {tab_file_name} | autosort_modified: {autosort_modified}')

                # Is Saved Tab
                elif view.file_name() and os.path.exists(view.file_name()) and not view.is_dirty():
                    autosort_saved = self.settings.get("autosort_saved_tabs", False)
                    if autosort_saved:
                        self.add_tab_to_queue()
                    if self.settings.get("print_on_tab_activation", False):
                        tab_file_name = os.path.basename(view.file_name())
                        print(f'name: {tab_file_name} | autosort_saved: {autosort_saved}')

                # Is Unsaved Tab
                elif not view.file_name():
                    autosort_unsaved = self.settings.get("autosort_unsaved_tabs", False)
                    if autosort_unsaved:
                        self.add_tab_to_queue()
                    if self.settings.get("print_on_tab_activation", False):
                        tab_file_name = "Untitled"
                        print(f'name: {tab_file_name} | autosort_unsaved: {autosort_unsaved}')

                # Else
                else:
                    print('Jorn_AutosortTabs: Activated [Non-file view]')
                    pass

        def on_selection_modified(self, view):
            pass # Not sure if i will use this later.

        def get_active_view_index(self):
            window = sublime.active_window()
            group = window.active_group()
            view = window.active_view_in_group(group)
            if view:
                _, index = window.get_view_index(view)
                return window, group, index, view
            return window, group, -1, None

        def add_tab_to_queue(self):
            window, group, index, view = self.get_active_view_index()
            print(f'window : {window}')
            print(f'group  : {group}')
            print(f'index  : {index}')
            print(f'view   : {view}')

        def move_active_to_top(self):
            window, group, index, view = self.get_active_view_index()
            if group == -1 or index == -1:
                return
            if index != 0:
                window.set_view_index(view, group, 0)
    ```
