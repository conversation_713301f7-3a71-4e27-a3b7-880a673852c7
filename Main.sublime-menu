[
    {
        // Ribbon-Menu: Preferences -> Package Settings
        "caption": "Preferences",
        "mnemonic": "n",
        "id": "preferences",
        "children":
        [
            {
                "caption": "Package Settings",
                "mnemonic": "P",
                "id": "package-settings",
                "children":
                [
                    {
                        // Jorn Tools
                        "caption": "Jorn Tools",
                        "mnemonic": "J",
                        "id": "jorn-tools",
                        "children": [
                            {
                                "caption": "Jorn_AutosortTabs",
                                "children":
                                [
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Main.sublime-menu",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_AutosortTabs/Main.sublime-menu"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Jorn_AutosortTabs.py",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.py"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Settings",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-settings"},
                                    },
                                    {
                                        "caption": "Settings – User",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/User/Default.sublime-settings"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Keymap",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-keymap"},
                                    },
                                    {
                                        "caption": "Keymap – User",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Commands",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-commands"},
                                    },
                                    {
                                        "caption": "Commands – User",
                                        "command": "open_file",
                                        "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                    {
                                        "caption": "Open Folder -> Todo",
                                        "args": {"cmd": ["explorer.exe", "${packages}/Jorn_AutosortTabs"]}
                                        // "command": "jorn_open_directory",
                                        // "args": {"group": -1, "index": -1 },
                                    },
                                    {/* --------------------------------------------- */ "caption": "-"},
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    },
    {
        // Ribbon-Menu: Jorn Tools
        "caption": "Jorn Tools",
        "mnemonic": "J",
        "id": "jorn-tools",
        "children": [
            {
                "caption": "Jorn_AutosortTabs",
                "children":
                [
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Main.sublime-menu",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_AutosortTabs/Main.sublime-menu"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Jorn_AutosortTabs.py",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.py"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Settings",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-settings"},
                    },
                    {
                        "caption": "Settings – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default.sublime-settings"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Keymap",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-keymap"},
                    },
                    {
                        "caption": "Keymap – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default (Windows).sublime-keymap"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Commands",
                        "command": "open_file",
                        "args": {"file": "${packages}/Jorn_AutosortTabs/Jorn_AutosortTabs.sublime-commands"},
                    },
                    {
                        "caption": "Commands – User",
                        "command": "open_file",
                        "args": {"file": "${packages}/User/Default (Windows).sublime-commands"},
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                    {
                        "caption": "Open Folder -> Todo",
                        "args": {"cmd": ["explorer.exe", "${packages}/Jorn_AutosortTabs"]}
                        // "command": "jorn_open_directory",
                        // "args": {"group": -1, "index": -1 },
                    },
                    {/* --------------------------------------------- */ "caption": "-"},
                ]
            }
        ]
    }
]

