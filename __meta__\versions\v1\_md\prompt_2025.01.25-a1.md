please write this into a plugin for sublime text in a class structure designed to retrieve info on open tabs:

    ```python
    import sublime
    import sublime_plugin

    # Logging
    # =======================================================
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s [%(levelname)s] %(message)s",
    )
    try:
        import concurrent.futures
    except ImportError:
        concurrent = None

    # Constants
    # =======================================================
    _SETTINGS_FILE = "Jorn_GetTabsInfo.sublime-settings"

    # Tab categories
    # =======================================================
    TAB_CATEGORIES = [
        "empty_and_deleted",
        "empty_and_unsaved",
        "deleted_not_empty",
        "unsaved_not_empty",
        "clean_and_external",
        "clean_and_project",
        "dirty_and_external",
        "dirty_and_project",
        "inactive_and_project",
        "inactive_and_external",
        "short_lived_and_project",
        "short_lived_and_external",
        "uncategorized",
    ]

    # Check if the file is in the current project
    # =======================================================
    def is_in_project(view_instance):
        file_path = view_instance.file_name()
        if not file_path:
            return False
        window_instance = view_instance.window()
        if not window_instance:
            return False
        project_folders = window_instance.folders()
        return any(
            os.path.commonpath([file_path, folder]) == folder
            for folder in project_folders
        )

    # Determine category of a given view
    # =======================================================
    def determine_category(view_instance, plugin_instance):
        content_stripped = view_instance.substr(sublime.Region(0, view_instance.size())).strip()
        content_length = len(content_stripped)
        is_dirty = view_instance.is_dirty()
        file_path = view_instance.file_name()
        in_project = is_in_project(view_instance)

        ):
        if not file_path:
            if content_length <= 1:
                return "empty_and_unsaved"
            else:
                return "unsaved_not_empty"
        else:
            if not os.path.exists(file_path):
                if content_length <= 1:
                    return "empty_and_deleted"
                else:
                    return "deleted_not_empty"
            else:
                if in_project:
                    return "dirty_and_project" if is_dirty else "clean_and_project"
                else:
                    return "dirty_and_external" if is_dirty else "clean_and_external"

    # Retrieve basic info about a tab
    # =======================================================
    def get_tab_info(view_instance):
        tab_info_map = {}
        try:
            if view_instance.file_name():
                tab_info_map["basename"] = os.path.basename(view_instance.file_name())
            else:
                tab_info_map["basename"] = "Untitled"
            tab_info_map["size_chars"] = view_instance.size()
        except Exception as e:
            tab_info_map["basename"] = "Unknown"
            tab_info_map["size_chars"] = -1
            print(f"[get_tab_info] Error: {e}")
            traceback.print_exc()
        return tab_info_map

    # Retrieve detailed info about a tab
    # =======================================================
    def get_tab_details(view_instance):
        details_map = {
            "basename": "Untitled",
            "group": 0,
            "index": 0,
            "syntax": "Plain text",
            "line_count": 0,
        }
        try:
            window_instance = view_instance.window()
            if window_instance:
                group_idx, tab_idx = window_instance.get_view_index(view_instance)
                details_map["group"] = group_idx
                details_map["index"] = tab_idx
            if view_instance.file_name():
                details_map["basename"] = os.path.basename(view_instance.file_name())
            else:
                details_map["basename"] = f"Untitled ({view_instance.size()} chars)"
            syntax_path = view_instance.settings().get("syntax")
            if syntax_path:
                syntax_file = os.path.basename(syntax_path)
                syntax_name, _ = os.path.splitext(syntax_file)
                details_map["syntax"] = syntax_name
            details_map["line_count"] = view_instance.rowcol(view_instance.size())[0] + 1
        except Exception as e:
            print(f"[get_tab_details] Error: {e}")
            traceback.print_exc()
        return details_map
    ```
