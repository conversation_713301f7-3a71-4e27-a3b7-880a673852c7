
Here's the output when pinning a tab:

    [DEBUG] on_activated_async: view_id=210, group=0, index=0
    [DEBUG] is_pinned check: view_id=210, result=False
    [DEBUG] Skipping unpinned view (no autosort)
    [DEBUG] on_activated_async: view_id=225, group=1, index=1
    [DEBUG] is_pinned check: view_id=225, result=False
    [DEBUG] Skipping unpinned view (no autosort)
    [DEBUG] is_pinned check: view_id=225, result=False
    [DEBUG] is_pinned check: view_id=225, result=False
    [DEBUG] is_pinned check: view_id=225, result=False
    [DEBUG] Pinning tab: view_id=225, group=1, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ColorSchemes_Jorn\Python\Jorn_Python_Purple_0.sublime-color-scheme
    [DEBUG] Updating tab decorations for window=11, group=1
    [DEBUG] Found 1 pinned tabs in this group: {225}
    [DEBUG] Current indicator style: both
    [DEBUG] Decorating pinned tab: view_id=225, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ColorSchemes_Jorn\Python\Jorn_Python_Purple_0.sublime-color-scheme
    [DEBUG] Added bookmark icon to 225
    [DEBUG] Set jorn_autosort_is_pinned=True for theme styling on 225
    [DEBUG] Attempting to apply color scheme to 225
    [DEBUG] Saved original color scheme: Jorn_Python_Dark_Focused.sublime-color-scheme
    [DEBUG] Setting color_scheme to Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme
    [DEBUG] After setting, color_scheme is now: Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=228, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\JornPinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=226, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-settings
    [DEBUG] Removing decorations from unpinned tab: view_id=227, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\JornPinnedTab.sublime-theme
    [DEBUG] Removing decorations from unpinned tab: view_id=229, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\Jorn_AutosortTabs.sublime-settings
    [DEBUG] Removing decorations from unpinned tab: view_id=230, file=C:\Users\<USER>\Desktop\SCRATCH\2025.04.17 - 09.52 -\2025.04.17 - 09.md
    [DEBUG] Removing decorations from unpinned tab: view_id=231, file=untitled



Here's the output when unpinning a tab:

    [DEBUG] is_pinned check: view_id=225, result=True
    [DEBUG] is_pinned check: view_id=225, result=True
    [DEBUG] is_pinned check: view_id=225, result=True
    [DEBUG] Unpinning tab: view_id=225, group=1, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ColorSchemes_Jorn\Python\Jorn_Python_Purple_0.sublime-color-scheme
    [DEBUG] Updating tab decorations for window=11, group=1
    [DEBUG] Found 0 pinned tabs in this group: set()
    [DEBUG] Current indicator style: both
    [DEBUG] Removing decorations from unpinned tab: view_id=225, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ColorSchemes_Jorn\Python\Jorn_Python_Purple_0.sublime-color-scheme
    [DEBUG] Restoring original color scheme for unpinned tab: Jorn_Python_Dark_Focused.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=228, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\JornPinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=226, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-settings
    [DEBUG] Removing decorations from unpinned tab: view_id=227, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\JornPinnedTab.sublime-theme
    [DEBUG] Removing decorations from unpinned tab: view_id=229, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\Jorn_AutosortTabs.sublime-settings
    [DEBUG] Removing decorations from unpinned tab: view_id=230, file=C:\Users\<USER>\Desktop\SCRATCH\2025.04.17 - 09.52 -\2025.04.17 - 09.md
    [DEBUG] Removing decorations from unpinned tab: view_id=231, file=untitled

---

The color-scheme works when pinning and unpinning, but the theme doesn't seem to have any effect