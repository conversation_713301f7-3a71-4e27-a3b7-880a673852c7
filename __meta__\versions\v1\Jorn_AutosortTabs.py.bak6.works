import sublime
import sublime_plugin
import os
from collections import defaultdict

PLUGIN_NAME = "Jorn_AutosortTabs"

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    """
    This EventListener handles:
     - Tracking pinned tabs in an in-memory data structure: self.pinned_tabs
     - When a tab is activated (via on_activated_async), checks plugin settings
       to see if it meets "autosort" conditions (deleted, saved, modified, unsaved).
     - If it is *not pinned* but meets autosort criteria, moves it to the "top" of
       the non-pinned section in the current group, preserving pinned tabs at the front.
    """

    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        # pinned_tabs is a nested structure:
        # { window_id: { group_index: set_of_pinned_view_ids } }
        self.pinned_tabs = defaultdict(lambda: defaultdict(set))
        Jorn_AutosortTabsCommand._instance = self

    @classmethod
    def instance(cls):
        """
        Returns the singleton instance of this EventListener
        so other commands can call pin_tab(...) or unpin_tab(...).
        """
        return cls._instance

    # -------------------------------------------------------------------------
    # Sublime Events
    # -------------------------------------------------------------------------
    def on_activated_async(self, view):
        """
        Called when a view is activated. If autosort is enabled and
        the view meets the sorting conditions (deleted, saved, etc.) but is
        not pinned, we move it to just below the pinned-tabs region.
        """
        if not self._should_process(view):
            return

        window = view.window()
        if not window:
            return

        group, _ = window.get_view_index(view)
        # Determine how many pinned tabs there are in this group
        max_topmost_level = self.get_max_topmost_level(window, group)

        # If this tab is not pinned, move it to 'max_topmost_level'.
        if not self._is_pinned(view):
            self.move_active_to_top(view, max_topmost_level)

    def on_close(self, view):
        """
        When a view closes, remove it from our pinned_tabs data structure
        (it no longer makes sense to keep it pinned if it doesn't exist).
        """
        view_id = view.id()
        for window_id in list(self.pinned_tabs.keys()):
            for group_id in list(self.pinned_tabs[window_id].keys()):
                self.pinned_tabs[window_id][group_id].discard(view_id)

    # -------------------------------------------------------------------------
    # Public API (pin/unpin)
    # -------------------------------------------------------------------------
    def pin_tab(self, view):
        """
        Mark the given view as 'pinned' in pinned_tabs.
        """
        window = view.window()
        if not window:
            return

        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].add(view.id())

    def unpin_tab(self, view):
        """
        Mark the given view as 'unpinned' in pinned_tabs.
        """
        window = view.window()
        if not window:
            return

        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].discard(view.id())

    # -------------------------------------------------------------------------
    # Internal Helpers
    # -------------------------------------------------------------------------
    def _should_process(self, view):
        """
        Returns True if:
          - view/window is valid
          - autosort_on_tab_activated is True
          - the view meets autosort conditions (deleted, saved, modified, unsaved)
        """
        if not view or not view.window():
            return False

        # Must also be enabled via settings
        if not self.settings.get("autosort_on_tab_activated", False):
            return False

        return self._meets_sorting_conditions(view)

    def _meets_sorting_conditions(self, view):
        """
        Checks if the view matches user-defined conditions (deleted, modified, etc.)
        per the plugin settings: autosort_deleted_tabs, autosort_modified_tabs, etc.
        """
        file_name = view.file_name()
        if not file_name:
            # Unsaved tab?
            return self.settings.get("autosort_unsaved_tabs", False)

        if not os.path.exists(file_name):
            # Deleted tab?
            return self.settings.get("autosort_deleted_tabs", False)

        if view.is_dirty():
            # Modified tab?
            return self.settings.get("autosort_modified_tabs", False)

        # Otherwise it's a saved tab
        return self.settings.get("autosort_saved_tabs", False)

    def _is_pinned(self, view):
        """
        Return True if the given view is pinned in pinned_tabs.
        """
        window = view.window()
        if not window:
            return False
        group, _ = window.get_view_index(view)
        return view.id() in self.pinned_tabs[window.id()][group]

    # -------------------------------------------------------------------------
    # Reordering logic
    # -------------------------------------------------------------------------
    def get_max_topmost_level(self, window, group):
        """
        Returns the number of pinned tabs in (window, group), i.e.
        how many tabs occupy the 'pinned' region at the top.
        """
        return len(self.pinned_tabs[window.id()][group])

    def move_active_to_top(self, view, start_index):
        """
        Moves the currently active (non-pinned) tab to index 'start_index'
        within its group (so it sits just below pinned tabs).

        Example:
        If pinned_tabs_count = 2, then pinned indices are [0,1].
        The non-pinned tabs start at index 2. So we do set_view_index(view, group, 2).
        """
        window = view.window()
        if not window:
            return

        group, current_index = window.get_view_index(view)
        # Only move it if it's *below* the pinned region:
        # i.e., if current_index > start_index, we want to push it up to 'start_index'.
        if current_index > start_index:
            window.set_view_index(view, group, start_index)
            # on_activated_async might fire again, but after the second activation,
            # current_index == start_index => no further move.


class JornAutosortPinTabCommand(sublime_plugin.TextCommand):
    """
    Pins the current tab via the plugin's pin_tab(...) API.
    """

    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]

        plugin = Jorn_AutosortTabsCommand.instance()
        if plugin:
            plugin.pin_tab(view)
            sublime.status_message("Jorn Autosort: Tab pinned.")


class JornAutosortUnpinTabCommand(sublime_plugin.TextCommand):
    """
    Unpins the current tab via the plugin's unpin_tab(...) API.
    """

    def run(self, edit, args=None, index=-1, group=-1, **kwargs):
        window = self.view.window()
        views_in_group = window.views_in_group(group)
        view = views_in_group[index] if index >= 0 else views_in_group[-1]
        plugin = Jorn_AutosortTabsCommand.instance()
        if plugin:
            plugin.unpin_tab(view)
            sublime.status_message("Jorn Autosort: Tab unpinned.")
