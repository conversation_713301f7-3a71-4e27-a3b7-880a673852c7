import sublime
import sublime_plugin

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    def __init__(self):
        self.last_activated_view = None

    def on_activated_async(self, view):
        self.last_activated_view = view  # Store the last activated view

    def on_modified_async(self, view):
        settings = sublime.load_settings('Jorn_AutosortTabs.sublime-settings')
        trigger_condition = settings.get("autosort_trigger_condition", "keypress")

        if trigger_condition == "keypress" and view == self.last_activated_view:
            self.move_active_to_top()
        elif trigger_condition == "always":
            self.move_active_to_top()

    def move_active_to_top(self):
        window = sublime.active_window()
        group, index = window.get_view_index(window.active_view())
        if index > 0:  # Only move if not already at the top
            window.set_view_index(window.active_view(), group, 0)
