import sublime
import sublime_plugin
import os
import fnmatch
import logging
from collections import defaultdict

PLUGIN_NAME = "Jorn_AutosortTabs"
logger = logging.getLogger(PLUGIN_NAME)

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    def __init__(self):
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self.pinned_tabs = defaultdict(lambda: defaultdict(list))  # window_id -> group_index -> [view_ids]
        self.sort_queue = []
        self.processing = False

    # ------------------------ Core Event Handlers ---------------------------
    def on_activated_async(self, view):
        """Handle tab activation with proper async processing"""
        if not self._should_process(view):
            return

        window = view.window()
        if not window:
            return

        window_id = window.id()
        group, _ = window.get_view_index(view)

        # Check if view is pinned
        if view.id() in self.pinned_tabs[window_id][group]:
            return

        # Queue sorting operation
        self._enqueue_sort(view)

        if self.settings.get("debug_logging", False):
            logger.debug(f"Queued sort for view {view.id()} in window {window_id} group {group}")

    # ------------------------ Pinned Tab Management --------------------------
    def pin_tab(self, view):
        """Add view to pinned tabs structure"""
        window = view.window()
        if not window:
            return

        window_id = window.id()
        group, _ = window.get_view_index(view)
        view_id = view.id()

        if view_id not in self.pinned_tabs[window_id][group]:
            self.pinned_tabs[window_id][group].append(view_id)
            if self.settings.get("debug_logging", False):
                logger.debug(f"Pinned view {view_id} in window {window_id} group {group}")

    def unpin_tab(self, view):
        """Remove view from pinned tabs structure"""
        window = view.window()
        if not window:
            return

        window_id = window.id()
        group, _ = window.get_view_index(view)
        view_id = view.id()

        if view_id in self.pinned_tabs[window_id][group]:
            self.pinned_tabs[window_id][group].remove(view_id)
            if self.settings.get("debug_logging", False):
                logger.debug(f"Unpinned view {view_id} in window {window_id} group {group}")

    # ------------------------ Sorting Operations -----------------------------
    def _enqueue_sort(self, view):
        """Add view to sort queue with debouncing"""
        self.sort_queue.append({
            'view_id': view.id(),
            'window_id': view.window().id() if view.window() else None,
            'group': view.window().get_view_index(view)[0] if view.window() else -1
        })

        if not self.processing:
            sublime.set_timeout_async(self._process_queue, 150)
            self.processing = True

    def _process_queue(self):
        """Process all queued sort operations"""
        while self.sort_queue:
            entry = self.sort_queue.pop(0)
            view = sublime.View(entry['view_id'])

            if view.is_valid() and view.window():
                self._sort_view(view, entry['window_id'], entry['group'])

        self.processing = False

    def _sort_view(self, view, window_id, group):
        """Perform actual view sorting"""
        window = view.window()
        current_group, current_index = window.get_view_index(view)

        # Only sort if in original group
        if current_group != group:
            return

        pinned_count = len(self.pinned_tabs[window_id][group])
        target_position = pinned_count if pinned_count <= current_index else current_index

        if current_index != target_position:
            window.set_view_index(view, group, target_position)
            if self.settings.get("debug_logging", False):
                logger.debug(f"Moved view {view.id()} to position {target_position}")

    # ------------------------ Helper Methods --------------------------------
    def _should_process(self, view):
        """Determine if view should be processed for sorting"""
        if not view or not view.window():
            return False

        conditions = [
            self.settings.get("autosort_on_tab_activated", False),
            not self._is_pinned(view),
            self._meets_sorting_conditions(view)
        ]

        return all(conditions)

    def _is_pinned(self, view):
        """Check if view is pinned"""
        window = view.window()
        if not window:
            return False

        window_id = window.id()
        group, _ = window.get_view_index(view)
        return view.id() in self.pinned_tabs[window_id][group]

    def _meets_sorting_conditions(self, view):
        """Check view state against settings"""
        if not view.file_name():
            return self.settings.get("autosort_unsaved_tabs", False)

        if not os.path.exists(view.file_name()):
            return self.settings.get("autosort_deleted_tabs", False)

        if view.is_dirty():
            return self.settings.get("autosort_modified_tabs", False)

        return self.settings.get("autosort_saved_tabs", False)

    def on_close(self, view):
        """Clean up closed views from tracking"""
        view_id = view.id()
        for window in self.pinned_tabs.values():
            for group in window.values():
                if view_id in group:
                    group.remove(view_id)

# ------------------------ Command Implementations --------------------------
class JornAutosortPinTabCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        plugin = sublime_plugin.find_event_listener(Jorn_AutosortTabsCommand)
        plugin.pin_tab(self.view)

class JornAutosortUnpinTabCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        plugin = sublime_plugin.find_event_listener(Jorn_AutosortTabsCommand)
        plugin.unpin_tab(self.view)

class JornAutosortDebugInfoCommand(sublime_plugin.TextCommand):
    def run(self, edit):
        plugin = sublime_plugin.find_event_listener(Jorn_AutosortTabsCommand)
        window = self.view.window()
        group, _ = window.get_view_index(self.view)
        pinned = plugin.pinned_tabs[window.id()][group]
        sublime.message_dialog(
            f"Pinned tabs in current group: {len(pinned)}\n"
            f"Total tracked windows: {len(plugin.pinned_tabs)}"
        )
