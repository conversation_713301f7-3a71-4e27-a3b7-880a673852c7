import sublime
import sublime_plugin
import os
from collections import defaultdict
from time import time

PLUGIN_NAME = "Jorn_AutosortTabs"

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self.pinned_tabs = defaultdict(lambda: defaultdict(set))
        self._is_reordering = False
        self.active_groups = set()
        self.group_cooldowns = {}
        Jorn_AutosortTabsCommand._instance = self

    @classmethod
    def instance(cls):
        return cls._instance

    def on_activated_async(self, view):
        if not view or not view.window():
            return

        window = view.window()
        group, cur_index = window.get_view_index(view)
        window_group_key = (window.id(), group)

        # Cooldown check
        COOLDOWN = 0.1
        last_activation = self.group_cooldowns.get(window_group_key, 0)
        if time() - last_activation < COOLDOWN:
            return
        self.group_cooldowns[window_group_key] = time()

        # Group validity check
        if group < 0 or group >= window.num_groups():
            return

        # Prevent re-entrancy for this specific window/group combination
        if window_group_key in self.active_groups:
            return

        self.active_groups.add(window_group_key)
        try:
            if self._is_pinned(view):
                self._reorder_group(window, group)
            elif self._should_process(view):
                pinned_count = self._pinned_count(window.id(), group)
                if cur_index > pinned_count:
                    window.set_view_index(view, group, pinned_count)
        finally:
            self.active_groups.remove(window_group_key)

    def on_close(self, view):
        view_id = view.id()
        for w_id, group_dict in list(self.pinned_tabs.items()):
            for g_id, pinned_set in list(group_dict.items()):
                pinned_set.discard(view_id)

    def on_post_window_command(self, window, command_name, args):
        if command_name in ("drag_select", "drag_drop", "move_tab"):
            self._sync_pinned_data(window)
            self._reorder_all_groups_in_window(window)

    def pin_tab(self, view):
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].add(view.id())
        self._reorder_group(window, group)

    def unpin_tab(self, view):
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].discard(view.id())
        self._reorder_group(window, group)

    def _should_process(self, view):
        if not self.settings.get("autosort_on_tab_activated", False):
            return False
        return self._meets_sorting_conditions(view)

    def _meets_sorting_conditions(self, view):
        file_name = view.file_name()
        if not file_name:
            return self.settings.get("autosort_unsaved_tabs", False)
        if not os.path.exists(file_name):
            return self.settings.get("autosort_deleted_tabs", False)
        if view.is_dirty():
            return self.settings.get("autosort_modified_tabs", False)
        return self.settings.get("autosort_saved_tabs", False)

    def _is_pinned(self, view):
        window = view.window()
        if not window:
            return False
        group, _ = window.get_view_index(view)
        return view.id() in self.pinned_tabs[window.id()][group]

    def _pinned_count(self, window_id, group_id):
        return len(self.pinned_tabs[window_id][group_id])

    def _reorder_group(self, window, group):
        if not window or group < 0 or group >= window.num_groups():
            return

        views = window.views_in_group(group)
        pinned_ids = self.pinned_tabs[window.id()][group]

        pinned_views = [v for v in views if v.id() in pinned_ids]
        unpinned_views = [v for v in views if v.id() not in pinned_ids]

        new_order = pinned_views + unpinned_views

        for idx, v in enumerate(new_order):
            cur_group, cur_index = window.get_view_index(v)
            if (cur_group != group) or (cur_index != idx):
                window.set_view_index(v, group, idx)

    def _reorder_all_groups_in_window(self, window):
        for g_id in range(window.num_groups()):
            self._reorder_group(window, g_id)

    def _sync_pinned_data(self, window):
        w_id = window.id()
        group_views = {group_index: {v.id() for v in window.views_in_group(group_index)}
                       for group_index in range(window.num_groups())}

        new_pinned = defaultdict(set)
        for group_index, pinned_set in self.pinned_tabs[w_id].items():
            for vid in pinned_set:
                for g_idx, view_ids_in_group in group_views.items():
                    if vid in view_ids_in_group:
                        new_pinned[g_idx].add(vid)
                        break
        self.pinned_tabs[w_id] = new_pinned

class JornAutosortTogglePinTabCommand(sublime_plugin.TextCommand):
    def run(self, edit, action="toggle", group=-1, index=-1, **kwargs):
        window = self.view.window()
        if not window:
            return

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return

        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return

        pinned = plugin._is_pinned(target_view)

        if action == "pin" and not pinned:
            plugin.pin_tab(target_view)
            sublime.status_message("[Jorn Autosort] Tab pinned.")
        elif action == "unpin" and pinned:
            plugin.unpin_tab(target_view)
            sublime.status_message("[Jorn Autosort] Tab unpinned.")
        elif action == "toggle":
            if pinned:
                plugin.unpin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab unpinned.")
            else:
                plugin.pin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab pinned.")
        else:
            sublime.status_message(f"[Jorn Autosort] No action taken.")

    def is_visible(self, action="toggle", group=-1, index=-1, **kwargs):
        if action not in ("pin", "unpin", "toggle"):
            return False

        window = self.view.window()
        if not window:
            return False

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return False

        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return False

        pinned = plugin._is_pinned(target_view)
        if action == "pin":
            return not pinned
        elif action == "unpin":
            return pinned
        return True
