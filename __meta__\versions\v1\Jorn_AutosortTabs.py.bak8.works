import sublime
import sublime_plugin
import os
from collections import defaultdict

PLUGIN_NAME = "Jorn_AutosortTabs"


class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")

        # pinned_tabs: { window_id: { group_index: set_of_view_ids } }
        self.pinned_tabs = defaultdict(lambda: defaultdict(set))

        # simple recursion guard to avoid repeated on_activated calls
        self._is_reordering = False

        Jorn_AutosortTabsCommand._instance = self

    @classmethod
    def instance(cls):
        return cls._instance

    # -------------------------------------------------------------------------
    # Sublime Event Hooks
    # -------------------------------------------------------------------------
    def on_activated_async(self, view):
        """
        If the view is pinned, reorder the group so pinned tabs stay at front.
        If the view is not pinned but meets conditions, move it behind pinned tabs.
        """
        if not view or not view.window() or self._is_reordering:
            return

        window = view.window()
        group, cur_index = window.get_view_index(view)

        # If pinned, reorder the group to ensure pinned tabs are all at the front.
        if self._is_pinned(view):
            self._is_reordering = True
            try:
                self._reorder_group(window, group)
            finally:
                self._is_reordering = False
            return

        # If the plugin is disabled or the view doesn't meet conditions, do nothing else.
        if not self._should_process(view):
            return

        # If unpinned but meets autosort conditions, move it just behind pinned tabs
        pinned_count = self._pinned_count(window.id(), group)
        if cur_index > pinned_count:
            self._is_reordering = True
            try:
                window.set_view_index(view, group, pinned_count)
            finally:
                self._is_reordering = False

    def on_close(self, view):
        view_id = view.id()
        for w_id, group_dict in list(self.pinned_tabs.items()):
            for g_id, pinned_set in list(group_dict.items()):
                pinned_set.discard(view_id)

    def on_post_window_command(self, window, command_name, args):
        if command_name in ("drag_select", "drag_drop", "move_tab"):
            self._sync_pinned_data(window)
            self._reorder_all_groups_in_window(window)

    # -------------------------------------------------------------------------
    # Pin/Unpin
    # -------------------------------------------------------------------------
    def pin_tab(self, view):
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].add(view.id())
        self._reorder_group(window, group)

    def unpin_tab(self, view):
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].discard(view.id())
        self._reorder_group(window, group)

    # -------------------------------------------------------------------------
    # Internal Helpers
    # -------------------------------------------------------------------------
    def _should_process(self, view):
        """
        Only autosort if:
          - 'autosort_on_tab_activated' is True,
          - The view matches user conditions (deleted, saved, etc.).
        """
        if not self.settings.get("autosort_on_tab_activated", False):
            return False
        return self._meets_sorting_conditions(view)

    def _meets_sorting_conditions(self, view):
        file_name = view.file_name()
        if not file_name:
            return self.settings.get("autosort_unsaved_tabs", False)
        if not os.path.exists(file_name):
            return self.settings.get("autosort_deleted_tabs", False)
        if view.is_dirty():
            return self.settings.get("autosort_modified_tabs", False)
        return self.settings.get("autosort_saved_tabs", False)

    def _is_pinned(self, view):
        window = view.window()
        if not window:
            return False
        group, _ = window.get_view_index(view)
        return view.id() in self.pinned_tabs[window.id()][group]

    def _pinned_count(self, window_id, group_id):
        return len(self.pinned_tabs[window_id][group_id])

    # This is the main reorder logic that ensures pinned tabs at front:
    def _reorder_group(self, window, group):
        views = window.views_in_group(group)
        pinned_ids = self.pinned_tabs[window.id()][group]

        pinned_views = []
        unpinned_views = []
        for v in views:
            if v.id() in pinned_ids:
                pinned_views.append(v)
            else:
                unpinned_views.append(v)

        # pinned first, then unpinned
        new_order = pinned_views + unpinned_views

        # Minimally apply index changes
        for idx, v in enumerate(new_order):
            cur_group, cur_index = window.get_view_index(v)
            if (cur_group != group) or (cur_index != idx):
                window.set_view_index(v, group, idx)

    def _reorder_all_groups_in_window(self, window):
        for g_id in range(window.num_groups()):
            self._reorder_group(window, g_id)

    def _sync_pinned_data(self, window):
        w_id = window.id()
        group_views = {}
        for group_index in range(window.num_groups()):
            group_views[group_index] = {v.id() for v in window.views_in_group(group_index)}

        new_pinned = defaultdict(set)
        for group_index, pinned_set in self.pinned_tabs[w_id].items():
            for vid in pinned_set:
                found_group = False
                for g_idx, view_ids_in_group in group_views.items():
                    if vid in view_ids_in_group:
                        new_pinned[g_idx].add(vid)
                        found_group = True
                        break
                if not found_group:
                    # view is no longer in this window => it's closed
                    pass
        self.pinned_tabs[w_id] = new_pinned



# -------------------------------------------------------------------------
# Single Toggle Command for Pin/Unpin
# -------------------------------------------------------------------------
class JornAutosortTogglePinTabCommand(sublime_plugin.TextCommand):
    """
    Command that can 'pin', 'unpin', or 'toggle' the pinned state of the
    tab that was right-clicked. 'group' and 'index' come from the tab context menu.
    """

    def run(self, edit, action="toggle", group=-1, index=-1, **kwargs):
        window = self.view.window()
        if not window:
            return

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return

        # Safe-guard index
        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return

        pinned = plugin._is_pinned(target_view)

        if action == "pin":
            if pinned:
                sublime.status_message("[Jorn Autosort] Tab is already pinned.")
            else:
                plugin.pin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab pinned.")
        elif action == "unpin":
            if pinned:
                plugin.unpin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab unpinned.")
            else:
                sublime.status_message("[Jorn Autosort] Tab is not pinned.")
        elif action == "toggle":
            if pinned:
                plugin.unpin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab unpinned.")
            else:
                plugin.pin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab pinned.")
        else:
            sublime.status_message(f"[Jorn Autosort] Unknown action: {action}")

    def is_visible(self, action="toggle", group=-1, index=-1, **kwargs):
        """
        Optionally control whether separate 'Pin' or 'Unpin' items
        appear in the context menu, or always show 'Toggle'.
        """
        if action not in ("pin", "unpin", "toggle"):
            return False

        window = self.view.window()
        if not window:
            return False

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return False

        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return False

        pinned = plugin._is_pinned(target_view)
        if action == "pin":
            return not pinned
        elif action == "unpin":
            return pinned
        return True  # 'toggle' is always visible
