import sublime
import sublime_plugin
import os
import time
from collections import defaultdict, deque

PLUGIN_NAME = "Jorn_AutosortTabs"
MAX_REORDERS_PER_SECOND = 3

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        self.pinned_tabs = defaultdict(lambda: defaultdict(set))
        self._is_reordering = False
        self._last_arrangements = defaultdict(lambda: defaultdict(tuple))
        self._reorder_timestamps = deque()
        Jorn_AutosortTabsCommand._instance = self

    @classmethod
    def instance(cls):
        return cls._instance

    def on_activated_async(self, view):
        if not self._should_process(view):
            return

        window = view.window()
        if not window:
            return

        group, _ = window.get_view_index(view)
        max_topmost_level = self.get_max_topmost_level(window, group)

        if not self._is_pinned(view):
            self.move_active_to_top(view, max_topmost_level)

    def get_max_topmost_level(self, window, group):
        return len(self.pinned_tabs[window.id()][group])

    def move_active_to_top(self, view, start_index):
        window = view.window()
        group, current_index = window.get_view_index(view)

        if current_index > start_index:
            window.set_view_index(view, group, start_index)

    def _should_process(self, view):
        if not view or not view.window():
            return False

        conditions = [
            self.settings.get("autosort_on_tab_activated", False),
            self._meets_sorting_conditions(view)
        ]

        return all(conditions)

    def _is_pinned(self, view):
        window = view.window()
        if not window:
            return False

        group, _ = window.get_view_index(view)
        return view.id() in self.pinned_tabs[window.id()][group]

    def _meets_sorting_conditions(self, view):
        if not view.file_name():
            return self.settings.get("autosort_unsaved_tabs", False)

        if not os.path.exists(view.file_name()):
            return self.settings.get("autosort_deleted_tabs", False)

        if view.is_dirty():
            return self.settings.get("autosort_modified_tabs", False)

        return self.settings.get("autosort_saved_tabs", False)

    def pin_tab(self, view):
        window = view.window()
        if not window:
            return

        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].add(view.id())

    def unpin_tab(self, view):
        window = view.window()
        if not window:
            return

        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].discard(view.id())

    def on_close(self, view):
        view_id = view.id()
        for window_tabs in self.pinned_tabs.values():
            for group_tabs in window_tabs.values():
                group_tabs.discard(view_id)

class JornAutosortPinTabCommand(sublime_plugin.TextCommand):
    def run(self, edit, **kwargs):
        plugin = Jorn_AutosortTabsCommand.instance()
        if plugin:
            plugin.pin_tab(self.view)

class JornAutosortUnpinTabCommand(sublime_plugin.TextCommand):
    def run(self, edit, **kwargs):
        plugin = Jorn_AutosortTabsCommand.instance()
        if plugin:
            plugin.unpin_tab(self.view)
