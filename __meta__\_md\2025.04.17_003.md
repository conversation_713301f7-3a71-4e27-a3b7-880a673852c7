
Here's the output when pinning a tab:

    [DEBUG] is_pinned check: view_id=268, result=False
    [DEBUG] is_pinned check: view_id=268, result=False
    [DEBUG] is_pinned check: view_id=268, result=False
    [DEBUG] Pinning tab: view_id=268, group=0, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_003.md
    [DEBUG] Updating tab decorations for window=11, group=0
    [DEBUG] Found 1 pinned tabs in this group: {268}
    [DEBUG] Current indicator style: both
    [DEBUG] Decorating pinned tab: view_id=268, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_003.md
    [DEBUG] Added bookmark icon to 268
    [DEBUG] Set jorn_autosort_is_pinned=True for theme styling on 268
    [DEBUG] THEME INFO: current_theme=ayu-dark.sublime-theme, jorn_autosort_is_pinned in theme_settings=True
    [DEBUG] Attempting to apply color scheme to 268
    [DEBUG] Saved original color scheme: Jorn_MD_Dark_Focused.sublime-color-scheme
    [DEBUG] Setting color_scheme to Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme
    [DEBUG] After setting, color_scheme is now: Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=266, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\__meta__\prompts\prompt-complexity-reducer-2_a.md
    [DEBUG] Removing decorations from unpinned tab: view_id=210, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.py
    [DEBUG] Removing decorations from unpinned tab: view_id=263, file=untitled
    [DEBUG] Removing decorations from unpinned tab: view_id=212, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-commands
    [DEBUG] Removing decorations from unpinned tab: view_id=257, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\__meta__\old_gpt\.prompts.unsorted\prompt-dev-x-002.md
    [DEBUG] Removing decorations from unpinned tab: view_id=258, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\__meta__\old_gpt\.prompts.unsorted\prompt-dev-x-001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=259, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\2024.07.15_full_prompt.md
    [DEBUG] Removing decorations from unpinned tab: view_id=260, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\2024.07.15__leaked_prompts\2024.07.15__leaked_prompts.py
    [DEBUG] Removing decorations from unpinned tab: view_id=261, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\2024.07.15__leaked_prompts\2024.07.15__22.10.py
    [DEBUG] Removing decorations from unpinned tab: view_id=209, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\PinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=213, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ayu-dark.sublime-theme
    [DEBUG] Removing decorations from unpinned tab: view_id=214, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=264, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_002.md
    [DEBUG] Removing decorations from unpinned tab: view_id=265, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__TODO\py__ChatGPTConversationstoMarkdown\.gpt\prompts\prompt-dev-x-001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=215, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ColorSchemes_Jorn\Jorn_Python_Dark_Focused_New_4_CommentsBrighter.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=216, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.md
    [DEBUG] Removing decorations from unpinned tab: view_id=217, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\.specstory\history\2025-04-17_10-32-plugin-component-inventory-and-purpose-analysis.md
    [DEBUG] Removing decorations from unpinned tab: view_id=218, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\.gitignore
    [DEBUG] Removing decorations from unpinned tab: view_id=219, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Tab Context.sublime-menu
    [DEBUG] Removing decorations from unpinned tab: view_id=220, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\_md\prompt_2025.02.13_b.md
    [DEBUG] Removing decorations from unpinned tab: view_id=221, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-keymap
    [DEBUG] Removing decorations from unpinned tab: view_id=222, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Library\_GPT\gpt_prompt_downloads\chatgpt_system_prompt\prompts\gpts\knowledge\P0tS3c\UsingMetasploit.md
    [DEBUG] Removing decorations from unpinned tab: view_id=223, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__MyDataRetriever\prompts\prompt_v1_01_a.md
    [DEBUG] Removing decorations from unpinned tab: view_id=224, file=untitled
    [DEBUG] on_activated_async: view_id=268, group=0, index=0
    [DEBUG] is_pinned check: view_id=268, result=True
    [DEBUG] View is pinned, reordering group
    [DEBUG] Updating tab decorations for window=11, group=0
    [DEBUG] Found 1 pinned tabs in this group: {268}
    [DEBUG] Current indicator style: both
    [DEBUG] Decorating pinned tab: view_id=268, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_003.md
    [DEBUG] Added bookmark icon to 268
    [DEBUG] Set jorn_autosort_is_pinned=True for theme styling on 268
    [DEBUG] THEME INFO: current_theme=ayu-dark.sublime-theme, jorn_autosort_is_pinned in theme_settings=True
    [DEBUG] Attempting to apply color scheme to 268
    [DEBUG] Setting color_scheme to Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme
    [DEBUG] After setting, color_scheme is now: Packages/Jorn_AutosortTabs/JornPinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=266, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\__meta__\prompts\prompt-complexity-reducer-2_a.md
    [DEBUG] Removing decorations from unpinned tab: view_id=210, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.py
    [DEBUG] Removing decorations from unpinned tab: view_id=263, file=untitled
    [DEBUG] Removing decorations from unpinned tab: view_id=212, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-commands
    [DEBUG] Removing decorations from unpinned tab: view_id=257, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\__meta__\old_gpt\.prompts.unsorted\prompt-dev-x-002.md
    [DEBUG] Removing decorations from unpinned tab: view_id=258, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\__meta__\old_gpt\.prompts.unsorted\prompt-dev-x-001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=259, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\2024.07.15_full_prompt.md
    [DEBUG] Removing decorations from unpinned tab: view_id=260, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\2024.07.15__leaked_prompts\2024.07.15__leaked_prompts.py
    [DEBUG] Removing decorations from unpinned tab: view_id=261, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\2024.07.15__leaked_prompts\2024.07.15__22.10.py
    [DEBUG] Removing decorations from unpinned tab: view_id=209, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\PinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=213, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ayu-dark.sublime-theme
    [DEBUG] Removing decorations from unpinned tab: view_id=214, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=264, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_002.md
    [DEBUG] Removing decorations from unpinned tab: view_id=265, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__TODO\py__ChatGPTConversationstoMarkdown\.gpt\prompts\prompt-dev-x-001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=215, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ColorSchemes_Jorn\Jorn_Python_Dark_Focused_New_4_CommentsBrighter.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=216, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.md
    [DEBUG] Removing decorations from unpinned tab: view_id=217, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\.specstory\history\2025-04-17_10-32-plugin-component-inventory-and-purpose-analysis.md
    [DEBUG] Removing decorations from unpinned tab: view_id=218, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\.gitignore
    [DEBUG] Removing decorations from unpinned tab: view_id=219, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Tab Context.sublime-menu
    [DEBUG] Removing decorations from unpinned tab: view_id=220, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\_md\prompt_2025.02.13_b.md
    [DEBUG] Removing decorations from unpinned tab: view_id=221, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-keymap
    [DEBUG] Removing decorations from unpinned tab: view_id=222, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Library\_GPT\gpt_prompt_downloads\chatgpt_system_prompt\prompts\gpts\knowledge\P0tS3c\UsingMetasploit.md
    [DEBUG] Removing decorations from unpinned tab: view_id=223, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__MyDataRetriever\prompts\prompt_v1_01_a.md
    [DEBUG] Removing decorations from unpinned tab: view_id=224, file=untitled

Here's the output when unpinning a tab:

    [DEBUG] is_pinned check: view_id=268, result=True
    [DEBUG] is_pinned check: view_id=268, result=True
    [DEBUG] is_pinned check: view_id=268, result=True
    [DEBUG] Unpinning tab: view_id=268, group=0, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_003.md
    [DEBUG] Updating tab decorations for window=11, group=0
    [DEBUG] Found 0 pinned tabs in this group: set()
    [DEBUG] Current indicator style: both
    [DEBUG] Removing decorations from unpinned tab: view_id=268, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_003.md
    [DEBUG] Restoring original color scheme for unpinned tab: Jorn_MD_Dark_Focused.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=266, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__VideoManager\py__AudioFromVideoExtractor\__meta__\prompts\prompt-complexity-reducer-2_a.md
    [DEBUG] Removing decorations from unpinned tab: view_id=210, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.py
    [DEBUG] Removing decorations from unpinned tab: view_id=263, file=untitled
    [DEBUG] Removing decorations from unpinned tab: view_id=212, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-commands
    [DEBUG] Removing decorations from unpinned tab: view_id=257, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\__meta__\old_gpt\.prompts.unsorted\prompt-dev-x-002.md
    [DEBUG] Removing decorations from unpinned tab: view_id=258, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\__meta__\old_gpt\.prompts.unsorted\prompt-dev-x-001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=259, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\2024.07.15_full_prompt.md
    [DEBUG] Removing decorations from unpinned tab: view_id=260, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\2024.07.15__leaked_prompts\2024.07.15__leaked_prompts.py
    [DEBUG] Removing decorations from unpinned tab: view_id=261, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__ProjectGenerator\src\templates\prompts\unsorted_gpt_prompts\2024.07.15__leaked_prompts\2024.07.15__22.10.py
    [DEBUG] Removing decorations from unpinned tab: view_id=209, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\PinnedTab.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=213, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ayu-dark.sublime-theme
    [DEBUG] Removing decorations from unpinned tab: view_id=214, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=264, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\__meta__\_md\2025.04.17_002.md
    [DEBUG] Removing decorations from unpinned tab: view_id=265, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__TODO\py__ChatGPTConversationstoMarkdown\.gpt\prompts\prompt-dev-x-001.md
    [DEBUG] Removing decorations from unpinned tab: view_id=215, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\User\ColorSchemes_Jorn\Jorn_Python_Dark_Focused_New_4_CommentsBrighter.sublime-color-scheme
    [DEBUG] Removing decorations from unpinned tab: view_id=216, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.md
    [DEBUG] Removing decorations from unpinned tab: view_id=217, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\.specstory\history\2025-04-17_10-32-plugin-component-inventory-and-purpose-analysis.md
    [DEBUG] Removing decorations from unpinned tab: view_id=218, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\.gitignore
    [DEBUG] Removing decorations from unpinned tab: view_id=219, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Tab Context.sublime-menu
    [DEBUG] Removing decorations from unpinned tab: view_id=220, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\_md\prompt_2025.02.13_b.md
    [DEBUG] Removing decorations from unpinned tab: view_id=221, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Apps\app_sublimetext\exe\Data\Packages\Jorn_AutosortTabs\Jorn_AutosortTabs.sublime-keymap
    [DEBUG] Removing decorations from unpinned tab: view_id=222, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Library\_GPT\gpt_prompt_downloads\chatgpt_system_prompt\prompts\gpts\knowledge\P0tS3c\UsingMetasploit.md
    [DEBUG] Removing decorations from unpinned tab: view_id=223, file=C:\Users\<USER>\Desktop\User\Nas_Flow_Jorn\__GOTO__\Scripts\Python\Py_Cli_Utils\py__MyDataRetriever\prompts\prompt_v1_01_a.md
    [DEBUG] Removing decorations from unpinned tab: view_id=224, file=untitled

---

The color-scheme works when pinning and unpinning, but the theme doesn't seem to have any effect