[
    {
        "class": "tab_control",
        "settings": ["jorn_autosort_is_pinned"],
        "attributes": ["!selected"],
        "layer0.tint": [60, 110, 180],  // Blue background
        "layer0.opacity": 0.3
    },
    {
        "class": "tab_control",
        "settings": ["jorn_autosort_is_pinned"],
        "attributes": ["selected"],
        "layer0.tint": [80, 140, 220],  // Brighter blue for selected
        "layer0.opacity": 0.5,
        "layer2.opacity": 1.0,
        "layer2.tint": [255, 165, 0]    // Orange indicator
    },
    {
        "class": "tab_control",
        "settings": ["jorn_autosort_is_pinned"],
        "layer3.tint": [255, 165, 0],   // Orange top border
        "layer3.opacity": 1.0,
        "layer3.inner_margin": [0, 2, 0, 0]
    },
    {
        "class": "tab_label",
        "parents": [
            {
                "class": "tab_control",
                "settings": ["jorn_autosort_is_pinned"]
            }
        ],
        "font.bold": true
    },
    {
        "class": "tab_label",
        "parents": [
            {
                "class": "tab_control",
                "settings": ["jorn_autosort_is_pinned"],
                "attributes": ["!selected"]
            }
        ],
        "fg": [240, 240, 255]  // Light blue-white text
    },
    {
        "class": "tab_label",
        "parents": [
            {
                "class": "tab_control",
                "settings": ["jorn_autosort_is_pinned"],
                "attributes": ["selected"]
            }
        ],
        "fg": [255, 255, 255]  // Pure white text for selected tabs
    }
]