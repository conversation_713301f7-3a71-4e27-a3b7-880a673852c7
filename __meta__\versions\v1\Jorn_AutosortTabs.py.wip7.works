import sublime
import sublime_plugin
import os
from collections import defaultdict

PLUGIN_NAME = "Jorn_AutosortTabs"

class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):
    _instance = None

    def __init__(self):
        super().__init__()
        self.settings = sublime.load_settings(f"{PLUGIN_NAME}.sublime-settings")
        # pinned_tabs: { window_id: { group_index: set_of_view_ids } }
        self.pinned_tabs = defaultdict(lambda: defaultdict(set))
        Jorn_AutosortTabsCommand._instance = self

    @classmethod
    def instance(cls):
        return cls._instance

    def on_activated_async(self, view):
        if not self._should_process(view):
            return

        window = view.window()
        if not window:
            return

        group, _ = window.get_view_index(view)
        max_topmost_level = self.get_max_topmost_level(window.id(), group)

        if not self._is_pinned(view):
            self.move_active_to_top(view, max_topmost_level)

    def on_close(self, view):
        view_id = view.id()
        for window_id in list(self.pinned_tabs.keys()):
            for group_id in list(self.pinned_tabs[window_id].keys()):
                self.pinned_tabs[window_id][group_id].discard(view_id)

    # Pin / Unpin
    def pin_tab(self, view):
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].add(view.id())

    def unpin_tab(self, view):
        window = view.window()
        if not window:
            return
        group, _ = window.get_view_index(view)
        self.pinned_tabs[window.id()][group].discard(view.id())

    def _should_process(self, view):
        if not view or not view.window():
            return False
        if not self.settings.get("autosort_on_tab_activated", False):
            return False
        return self._meets_sorting_conditions(view)

    def _meets_sorting_conditions(self, view):
        file_name = view.file_name()
        if not file_name:
            return self.settings.get("autosort_unsaved_tabs", False)
        if not os.path.exists(file_name):
            return self.settings.get("autosort_deleted_tabs", False)
        if view.is_dirty():
            return self.settings.get("autosort_modified_tabs", False)
        return self.settings.get("autosort_saved_tabs", False)

    def _is_pinned(self, view):
        window = view.window()
        if not window:
            return False
        group, _ = window.get_view_index(view)
        return view.id() in self.pinned_tabs[window.id()][group]

    def get_max_topmost_level(self, window_id, group):
        return len(self.pinned_tabs[window_id][group])

    def move_active_to_top(self, view, start_index):
        window = view.window()
        if not window:
            return
        group, current_index = window.get_view_index(view)
        if current_index > start_index:
            window.set_view_index(view, group, start_index)

class JornAutosortTogglePinTabCommand(sublime_plugin.TextCommand):
    """
    A single command to Pin/Unpin a tab or Toggle its state, depending on `action`.
    Valid actions: 'pin', 'unpin', 'toggle'.
    This command targets the tab that was right-clicked (i.e., the one identified by
    `group` and `index` in the context menu).
    """

    def run(self, edit, action="toggle", group=-1, index=-1, **kwargs):
        window = self.view.window()
        if not window:
            return

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return

        # If index is out of range, default to the last view in the group.
        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return

        pinned = plugin._is_pinned(target_view)

        if action == "pin":
            if not pinned:
                plugin.pin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab pinned.")
            else:
                sublime.status_message("[Jorn Autosort] Tab is already pinned.")

        elif action == "unpin":
            if pinned:
                plugin.unpin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab unpinned.")
            else:
                sublime.status_message("[Jorn Autosort] Tab is not pinned.")

        elif action == "toggle":
            if pinned:
                plugin.unpin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab unpinned.")
            else:
                plugin.pin_tab(target_view)
                sublime.status_message("[Jorn Autosort] Tab pinned.")

        else:
            sublime.status_message(f"[Jorn Autosort] Unknown action: {action}")

    def is_visible(self, action="toggle", group=-1, index=-1, **kwargs):
        """
        (Optional) Dynamically shows or hides context menu items based on pinned status.
        For example, if 'action' == 'pin', only show if the tab is not pinned.
        If 'action' == 'unpin', only show if the tab is pinned.
        If 'action' == 'toggle', always show.
        """
        if action not in ("pin", "unpin", "toggle"):
            return False

        window = self.view.window()
        if not window:
            return False

        views_in_group = window.views_in_group(group)
        if not views_in_group:
            return False

        if index < 0 or index >= len(views_in_group):
            target_view = views_in_group[-1]
        else:
            target_view = views_in_group[index]

        plugin = Jorn_AutosortTabsCommand.instance()
        if not plugin:
            return False

        pinned = plugin._is_pinned(target_view)

        if action == "pin":
            return not pinned
        elif action == "unpin":
            return pinned
        # 'toggle' is always visible
        return True
