

Instead of having two different commands (`JornAutosortPinTabCommand`, `JornAutosortUnpinTabCommand`), wouldn't it be preferred to have a single command that works like a "toggle"? I've provided an (unrelated) example below to illustrate:

    ```markdown
    ## `Jorn_LockPanes.py`

        ```
        class JornLockPanesCommand(sublime_plugin.WindowCommand):
            """
            Lock/Unlock current pane based on filetypes
            """

            def run(self, action):
                view = self.window.active_view()
                if not view:
                    return

                window_id = self.window.id()
                group_idx, _ = self.window.get_view_index(view)
                locked = JornLockPanes.is_group_locked(window_id, group_idx)

                if action == "lock" and not locked:
                    state = JornLockPanes.toggle_group_lock(window_id, group_idx)
                    msg = f"Group {group_idx} in Window {window_id} {state}."
                elif action == "unlock" and locked:
                    state = JornLockPanes.toggle_group_lock(window_id, group_idx)
                    msg = f"Group {group_idx} in Window {window_id} {state}."
                elif action == "toggle":
                    state = JornLockPanes.toggle_group_lock(window_id, group_idx)
                    msg = f"Group {group_idx} in Window {window_id} {state}."
                else:
                    state = "LOCKED" if locked else "UNLOCKED"
                    msg = f"Group {group_idx} in Window {window_id} already {state}."

                sublime.status_message(f"[{PLUGIN_NAME}] {msg}")
                logger.info(msg)

            def is_visible(self, action):
                """
                Dynamically updated context menu
                - Hides the lock/unlock group depending on the current group lock state.
                """
                view = self.window.active_view()
                if not view:
                    return False

                window_id = self.window.id()
                group_idx, _ = self.window.get_view_index(view)
                locked = JornLockPanes.is_group_locked(window_id, group_idx)

                if action == "lock":
                    return not locked
                if action == "unlock":
                    return locked
                return True
        ```

    ## `Tab Context.sublime-menu`


        {
            "caption": "Jorn Lock Panes: Lock Current Group",
            "command": "jorn_lock_panes",
            "args": { "action": "lock" }
        },
        {
            "caption": "Jorn Lock Panes: Unlock Current Group",
            "command": "jorn_lock_panes",
            "args": { "action": "unlock" }
        },
    ```
