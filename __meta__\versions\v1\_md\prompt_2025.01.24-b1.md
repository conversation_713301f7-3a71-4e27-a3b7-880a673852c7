
The goal is propose an optimal way to ensure stable and concistent handling of "sticky tabs".

Here's the current code for the Sublime Text plugin:

    # Project Files Documentation for `Jorn_AutosortTabs`

    ### File Structure

    ```
    ├── Jorn_AutosortTabs.py
    ├── Jorn_AutosortTabs.sublime-commands
    ├── Jorn_AutosortTabs.sublime-keymap
    ├── Jorn_AutosortTabs.sublime-project
    ├── Jorn_AutosortTabs.sublime-settings
    └── Tab Context.sublime-menu
    ```

    #### `Jorn_AutosortTabs.py`

    ```python
    import sublime
    import sublime_plugin
    import os
    import fnmatch

    # Constants
    # =======================================================
    _SETTINGS_FILE = "Jorn_AutosortTabs.sublime-settings"

    # Command to toggle sticky (pinned) status of the active tab
    # =======================================================
    class JornToggleStickyTabCommand(sublime_plugin.WindowCommand):
        """
        Toggles whether the current file is pinned (sticky) or not,
        storing that info in the plugin settings.
        """
        def run(self):
            settings = sublime.load_settings(_SETTINGS_FILE)
            enable_sticky = settings.get("enable_sticky_tabs", False)
            if not enable_sticky:
                sublime.message_dialog("Sticky tabs feature is currently disabled in settings.")
                return

            # Get current file
            view = self.window.active_view()
            if not view or not view.file_name():
                sublime.message_dialog("Cannot pin/unpin a non-file view.")
                return

            file_path = view.file_name()
            pinned_list = settings.get("sticky_tabs_list", [])

            if file_path in pinned_list:
                # Unpin
                pinned_list.remove(file_path)
                sublime.status_message("Unpinned: " + os.path.basename(file_path))
            else:
                # Pin
                pinned_list.append(file_path)
                sublime.status_message("Pinned: " + os.path.basename(file_path))

            settings.set("sticky_tabs_list", pinned_list)
            sublime.save_settings(_SETTINGS_FILE)

    # Main Autosort EventListener
    # =======================================================
    class Jorn_AutosortTabsCommand(sublime_plugin.EventListener):

        def __init__(self):
            # Load settings
            self.settings = sublime.load_settings(_SETTINGS_FILE)

            # Instance variables
            self.tab_file_path = ""
            self.tab_file_name = ""

        # Event: On tab activated
        # -------------------------------------------------------
        def on_activated(self, view):
            # Perform "move active tab to top" logic if enabled
            if self.settings.get("autosort_on_tab_activated", False):
                self.move_active_to_top()

            # Print info if enabled
            if self.settings.get("print_on_tab_activation", False):
                if view and view.window() and view.file_name():
                    self.tab_file_path = view.file_name()
                    self.tab_file_name = os.path.basename(self.tab_file_path)
                    print(f'Activated: [{self.tab_file_name}]')
                elif view and view.window():
                    print('Activated: [Non-file view]')

        # Event: On selection modified
        # -------------------------------------------------------
        def on_selection_modified(self, view):
            # Currently unused, but kept to retain prior structure.
            pass

        # Helper: Check if view is valid
        # -------------------------------------------------------
        def is_valid_file_view(self, view):
            # Ensure the view is a text buffer and has a file associated with it
            return view.file_name() is not None and not view.is_scratch()

        # Helper: Move the active tab to the top (or pinned region)
        # -------------------------------------------------------
        def move_active_to_top(self):
            window, group, index, view = self.get_active_view_index()
            if group == -1 or index == -1 or not view:
                return

            # If there's no file, do nothing
            file_path = view.file_name()
            if not file_path:
                return

            # If current tab is pinned
            if self.is_sticky_tab(file_path):
                # Optionally reorder pinned among themselves
                reorder_pinned = self.settings.get("reorder_pinned_on_activation", False)
                if reorder_pinned:
                    self.reorder_pinned_tab(view, window, group)
                # If we do NOT reorder pinned on activation, do nothing
            else:
                # Move this non-pinned tab right after the pinned tabs
                pinned_count = self.count_pinned_tabs_at_front(window, group)
                current_index = window.get_view_index(view)[1]
                if current_index != pinned_count:
                    window.set_view_index(view, group, pinned_count)

        # Helper: Returns references to the active window, group, index, and view
        # -------------------------------------------------------
        def get_active_view_index(self):
            window = sublime.active_window()
            group = window.active_group()
            view = window.active_view_in_group(group)
            if view:
                _, index = window.get_view_index(view)
                return window, group, index, view
            return window, group, -1, None

        # Helper: Check if a given file path is pinned
        # -------------------------------------------------------
        def is_sticky_tab(self, file_path):
            """
            Determines if the file_path is pinned via:
              - an explicit pinned list
              - pattern matching
            """
            if not file_path:
                return False

            enable_sticky = self.settings.get("enable_sticky_tabs", False)
            if not enable_sticky:
                return False

            pinned_list = self.settings.get("sticky_tabs_list", [])
            pinned_patterns = self.settings.get("sticky_tabs_patterns", [])

            # Check explicit pinned paths
            if file_path in pinned_list:
                return True

            # Check pattern-based pinning (filename only or entire path)
            filename_only = os.path.basename(file_path)
            for pat in pinned_patterns:
                # Example with fnmatch:
                if fnmatch.fnmatch(filename_only, pat):
                    return True

                # Or if you prefer to match the entire path:
                # if fnmatch.fnmatch(file_path, pat):
                #    return True

            return False

        # Helper: Count pinned tabs at the front of the tab list
        # -------------------------------------------------------
        def count_pinned_tabs_at_front(self, window, group):
            """
            Returns how many pinned tabs are at the very front
            of the tab list in the specified group.
            """
            count = 0
            for v in window.views_in_group(group):
                if self.is_sticky_tab(v.file_name()):
                    count += 1
                else:
                    # Once we hit the first non-pinned, we stop
                    break
            return count

        # Helper: Reorder pinned tabs among themselves
        # -------------------------------------------------------
        def reorder_pinned_tab(self, view, window, group):
            """
            Moves the reactivated pinned tab to the front among
            other pinned tabs (index 0 in pinned section), if enabled.
            """
            pinned_views = []
            non_pinned_views = []

            # Separate pinned from non-pinned in order
            for v in window.views_in_group(group):
                (pinned_views if self.is_sticky_tab(v.file_name()) else non_pinned_views).append(v)

            # If this view is among pinned views, move it to front of pinned list
            if view in pinned_views:
                pinned_views.remove(view)
                pinned_views.insert(0, view)

            # Now reorder them in the tab bar: pinned first, then non-pinned
            new_order = pinned_views + non_pinned_views
            for idx, v in enumerate(new_order):
                window.set_view_index(v, group, idx)
    ```

    #### `Jorn_AutosortTabs.sublime-commands`

    ```sublime-commands
    [
        {
            "caption": "Jorn - Toggle Sticky Tab",
            "command": "jorn_toggle_sticky_tab"
        }
    ]
    ```

    #### `Jorn_AutosortTabs.sublime-keymap`

    ```sublime-keymap
    [
    ]
    ```

    #### `Jorn_AutosortTabs.sublime-project`

    ```sublime-project
    {
        "folders":
        [
            {
                "path": "."
            }
        ]
    }
    ```

    #### `Jorn_AutosortTabs.sublime-settings`

    ```sublime-settings
    {
        // Existing behavior toggles
        "autosort_on_tab_activated": true,
        "print_on_tab_activation": false,

        // NEW: Whether pinned (sticky) tabs are enabled
        "enable_sticky_tabs": true,

        // NEW: A list of explicit file paths to keep pinned
        // You can store absolute paths or filenames if they are unique.
        // For example: "C:/myProject/main.py" or just "README.md"
        "sticky_tabs_list": [
            // "/absolute/path/to/yourFile.py",
            // "README.md"
        ],

        // NEW: Pattern-based pinning (fnmatch for filenames)
        // Example patterns:
        //   "*.log" or "*_spec.rb" or ".*config.*"
        "sticky_tabs_patterns": [
            // "*.log",
            // ".*config.*"
        ],

        // NEW: If true, the pinned tab you just activated is moved
        // to the front among pinned tabs themselves.
        "reorder_pinned_on_activation": false
    }
    ```

    #### `Tab Context.sublime-menu`

    ```sublime-menu
    // File: Tab Context.sublime-menu
    [
        { "caption": "-" },
        {
            "caption": "Toggle Sticky Tab",
            "command": "jorn_toggle_sticky_tab"
        }
    ]
    ```